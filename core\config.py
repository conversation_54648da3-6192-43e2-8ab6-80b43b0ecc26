"""
量化交易系统配置管理模块
"""
import configparser
import os
from typing import Dict, Any
from dataclasses import dataclass


@dataclass
class QMTConfig:
    """QMT客户端配置"""
    path: str = "D:\\恒泰证券迅投QMT"
    account_id: str = "YOUR_ACCOUNT_ID"
    account_type: str = "STOCK"


@dataclass
class DatabaseConfig:
    """数据库配置"""
    db_path: str = "data/trading_system.db"
    backup_interval: int = 3600  # 备份间隔（秒）


@dataclass
class LogConfig:
    """日志配置"""
    log_level: str = "INFO"
    log_file: str = "logs/trading_system.log"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5


@dataclass
class RiskConfig:
    """风控配置"""
    max_position_ratio: float = 0.3  # 单只股票最大仓位比例
    max_daily_loss: float = 0.05  # 单日最大亏损比例
    max_total_loss: float = 0.20  # 总最大亏损比例
    stop_loss_ratio: float = 0.02  # 默认止损比例
    take_profit_ratio: float = 0.05  # 默认止盈比例
    max_leverage: float = 1.0  # 最大杠杆倍数


@dataclass
class TradingConfig:
    """交易配置"""
    trading_start_time: str = "09:30:00"
    trading_end_time: str = "15:00:00"
    lunch_break_start: str = "11:30:00"
    lunch_break_end: str = "13:00:00"
    order_timeout: int = 30  # 订单超时时间（秒）
    max_retry_times: int = 3  # 最大重试次数
    min_order_amount: int = 100  # 最小下单数量（股）


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config/system.ini"):
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self._load_config()
        
        # 初始化各模块配置
        self.qmt = QMTConfig()
        self.database = DatabaseConfig()
        self.log = LogConfig()
        self.risk = RiskConfig()
        self.trading = TradingConfig()
        
        self._update_from_config()
    
    def _load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            self.config.read(self.config_file, encoding='utf-8')
        else:
            self._create_default_config()
    
    def _create_default_config(self):
        """创建默认配置文件"""
        os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
        
        # QMT配置
        self.config['QMT'] = {
            'path': 'D:\\恒泰证券迅投QMT',
            'account_id': 'YOUR_ACCOUNT_ID',
            'account_type': 'STOCK'
        }
        
        # 数据库配置
        self.config['DATABASE'] = {
            'db_path': 'data/trading_system.db',
            'backup_interval': '3600'
        }
        
        # 日志配置
        self.config['LOG'] = {
            'log_level': 'INFO',
            'log_file': 'logs/trading_system.log',
            'max_file_size': '********',
            'backup_count': '5'
        }
        
        # 风控配置
        self.config['RISK'] = {
            'max_position_ratio': '0.3',
            'max_daily_loss': '0.05',
            'max_total_loss': '0.20',
            'stop_loss_ratio': '0.02',
            'take_profit_ratio': '0.05',
            'max_leverage': '1.0'
        }
        
        # 交易配置
        self.config['TRADING'] = {
            'trading_start_time': '09:30:00',
            'trading_end_time': '15:00:00',
            'lunch_break_start': '11:30:00',
            'lunch_break_end': '13:00:00',
            'order_timeout': '30',
            'max_retry_times': '3',
            'min_order_amount': '100'
        }
        
        self._save_config()
    
    def _update_from_config(self):
        """从配置文件更新配置对象"""
        if 'QMT' in self.config:
            self.qmt.path = self.config['QMT'].get('path', self.qmt.path)
            self.qmt.account_id = self.config['QMT'].get('account_id', self.qmt.account_id)
            self.qmt.account_type = self.config['QMT'].get('account_type', self.qmt.account_type)
        
        if 'DATABASE' in self.config:
            self.database.db_path = self.config['DATABASE'].get('db_path', self.database.db_path)
            self.database.backup_interval = self.config['DATABASE'].getint('backup_interval', self.database.backup_interval)
        
        if 'LOG' in self.config:
            self.log.log_level = self.config['LOG'].get('log_level', self.log.log_level)
            self.log.log_file = self.config['LOG'].get('log_file', self.log.log_file)
            self.log.max_file_size = self.config['LOG'].getint('max_file_size', self.log.max_file_size)
            self.log.backup_count = self.config['LOG'].getint('backup_count', self.log.backup_count)
        
        if 'RISK' in self.config:
            self.risk.max_position_ratio = self.config['RISK'].getfloat('max_position_ratio', self.risk.max_position_ratio)
            self.risk.max_daily_loss = self.config['RISK'].getfloat('max_daily_loss', self.risk.max_daily_loss)
            self.risk.max_total_loss = self.config['RISK'].getfloat('max_total_loss', self.risk.max_total_loss)
            self.risk.stop_loss_ratio = self.config['RISK'].getfloat('stop_loss_ratio', self.risk.stop_loss_ratio)
            self.risk.take_profit_ratio = self.config['RISK'].getfloat('take_profit_ratio', self.risk.take_profit_ratio)
            self.risk.max_leverage = self.config['RISK'].getfloat('max_leverage', self.risk.max_leverage)
        
        if 'TRADING' in self.config:
            self.trading.trading_start_time = self.config['TRADING'].get('trading_start_time', self.trading.trading_start_time)
            self.trading.trading_end_time = self.config['TRADING'].get('trading_end_time', self.trading.trading_end_time)
            self.trading.lunch_break_start = self.config['TRADING'].get('lunch_break_start', self.trading.lunch_break_start)
            self.trading.lunch_break_end = self.config['TRADING'].get('lunch_break_end', self.trading.lunch_break_end)
            self.trading.order_timeout = self.config['TRADING'].getint('order_timeout', self.trading.order_timeout)
            self.trading.max_retry_times = self.config['TRADING'].getint('max_retry_times', self.trading.max_retry_times)
            self.trading.min_order_amount = self.config['TRADING'].getint('min_order_amount', self.trading.min_order_amount)
    
    def _save_config(self):
        """保存配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            self.config.write(f)
    
    def update_config(self, section: str, key: str, value: str):
        """更新配置项"""
        if section not in self.config:
            self.config[section] = {}
        self.config[section][key] = value
        self._save_config()
        self._update_from_config()
    
    def get_config(self, section: str, key: str, default: Any = None) -> Any:
        """获取配置项"""
        return self.config.get(section, key, fallback=default)


# 全局配置实例
config_manager = ConfigManager()
