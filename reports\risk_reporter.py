"""
风险报告生成模块
"""
import os
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from utils.logger import get_logger
from utils.database import db_manager
from core.risk_manager import RiskLevel

logger = get_logger("RiskReporter")

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class RiskReporter:
    """风险报告生成器"""
    
    def __init__(self, output_dir: str = "reports/risk_output"):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
    
    def generate_risk_report(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """生成风险报告"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y-%m-%d')
        if start_date is None:
            start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        
        logger.info(f"生成风险报告: {start_date} 到 {end_date}")
        
        try:
            # 获取风险事件数据
            risk_events_df = self._get_risk_events(start_date, end_date)
            
            # 获取交易数据
            trades_df = db_manager.get_trades(
                start_date=f"{start_date} 00:00:00",
                end_date=f"{end_date} 23:59:59"
            )
            
            # 生成报告数据
            report_data = {
                'period': {'start_date': start_date, 'end_date': end_date},
                'generated_time': datetime.now().isoformat(),
                'risk_summary': self._generate_risk_summary(risk_events_df),
                'risk_events_analysis': self._analyze_risk_events(risk_events_df),
                'portfolio_risk': self._analyze_portfolio_risk(trades_df),
                'risk_trends': self._analyze_risk_trends(risk_events_df),
                'recommendations': self._generate_risk_recommendations(risk_events_df, trades_df)
            }
            
            # 保存报告
            report_file = os.path.join(
                self.output_dir, 
                f"risk_report_{start_date}_to_{end_date}.json"
            )
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2, default=str)
            
            # 生成图表
            self._generate_risk_charts(report_data, start_date, end_date)
            
            logger.info(f"风险报告生成完成: {report_file}")
            return report_data
            
        except Exception as e:
            logger.error(f"生成风险报告失败: {e}")
            return {}
    
    def generate_daily_risk_summary(self, date: str = None) -> Dict[str, Any]:
        """生成日度风险摘要"""
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')
        
        logger.info(f"生成日度风险摘要: {date}")
        
        try:
            # 获取当日风险事件
            risk_events_df = self._get_risk_events(date, date)
            
            # 获取当日交易数据
            trades_df = db_manager.get_trades(
                start_date=f"{date} 00:00:00",
                end_date=f"{date} 23:59:59"
            )
            
            # 生成摘要数据
            summary_data = {
                'date': date,
                'generated_time': datetime.now().isoformat(),
                'risk_events_count': len(risk_events_df),
                'critical_events': len(risk_events_df[risk_events_df['level'] == 'critical']) if not risk_events_df.empty else 0,
                'high_risk_events': len(risk_events_df[risk_events_df['level'] == 'high']) if not risk_events_df.empty else 0,
                'trading_volume': trades_df['quantity'].sum() if not trades_df.empty else 0,
                'trading_amount': trades_df['amount'].sum() if not trades_df.empty else 0,
                'risk_level': self._assess_daily_risk_level(risk_events_df, trades_df),
                'key_risks': self._identify_key_risks(risk_events_df),
                'action_items': self._generate_action_items(risk_events_df)
            }
            
            # 保存摘要
            summary_file = os.path.join(self.output_dir, f"daily_risk_summary_{date}.json")
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary_data, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"日度风险摘要生成完成: {summary_file}")
            return summary_data
            
        except Exception as e:
            logger.error(f"生成日度风险摘要失败: {e}")
            return {}
    
    def _get_risk_events(self, start_date: str, end_date: str) -> pd.DataFrame:
        """获取风险事件数据"""
        try:
            with db_manager.get_connection() as conn:
                query = """
                    SELECT * FROM risk_events 
                    WHERE DATE(timestamp) BETWEEN ? AND ?
                    ORDER BY timestamp DESC
                """
                return pd.read_sql_query(query, conn, params=[start_date, end_date])
        except Exception as e:
            logger.error(f"获取风险事件数据失败: {e}")
            return pd.DataFrame()
    
    def _generate_risk_summary(self, risk_events_df: pd.DataFrame) -> Dict[str, Any]:
        """生成风险摘要"""
        if risk_events_df.empty:
            return {
                'total_events': 0,
                'events_by_level': {},
                'most_frequent_type': None,
                'risk_score': 0
            }
        
        # 按风险等级统计
        events_by_level = risk_events_df['level'].value_counts().to_dict()
        
        # 按事件类型统计
        events_by_type = risk_events_df['event_type'].value_counts().to_dict()
        most_frequent_type = max(events_by_type.keys()) if events_by_type else None
        
        # 计算风险评分
        risk_score = self._calculate_risk_score(events_by_level)
        
        return {
            'total_events': len(risk_events_df),
            'events_by_level': events_by_level,
            'events_by_type': events_by_type,
            'most_frequent_type': most_frequent_type,
            'risk_score': risk_score,
            'risk_rating': self._get_risk_rating(risk_score)
        }
    
    def _analyze_risk_events(self, risk_events_df: pd.DataFrame) -> Dict[str, Any]:
        """分析风险事件"""
        if risk_events_df.empty:
            return {}
        
        # 时间分布分析
        risk_events_df['date'] = pd.to_datetime(risk_events_df['timestamp']).dt.date
        risk_events_df['hour'] = pd.to_datetime(risk_events_df['timestamp']).dt.hour
        
        daily_distribution = risk_events_df.groupby('date').size().to_dict()
        hourly_distribution = risk_events_df.groupby('hour').size().to_dict()
        
        # 按股票分析
        symbol_risk = {}
        if 'symbol' in risk_events_df.columns:
            symbol_events = risk_events_df[risk_events_df['symbol'].notna()]
            if not symbol_events.empty:
                symbol_risk = symbol_events.groupby('symbol').agg({
                    'level': lambda x: x.value_counts().to_dict(),
                    'event_type': lambda x: x.value_counts().to_dict()
                }).to_dict('index')
        
        # 按策略分析
        strategy_risk = {}
        if 'strategy_name' in risk_events_df.columns:
            strategy_events = risk_events_df[risk_events_df['strategy_name'].notna()]
            if not strategy_events.empty:
                strategy_risk = strategy_events.groupby('strategy_name').agg({
                    'level': lambda x: x.value_counts().to_dict(),
                    'event_type': lambda x: x.value_counts().to_dict()
                }).to_dict('index')
        
        return {
            'daily_distribution': {str(k): v for k, v in daily_distribution.items()},
            'hourly_distribution': hourly_distribution,
            'symbol_risk': symbol_risk,
            'strategy_risk': strategy_risk,
            'peak_risk_hour': max(hourly_distribution.keys(), key=hourly_distribution.get) if hourly_distribution else None
        }
    
    def _analyze_portfolio_risk(self, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """分析投资组合风险"""
        if trades_df.empty:
            return {}
        
        # 计算持仓集中度
        position_concentration = self._calculate_position_concentration(trades_df)
        
        # 计算交易频率风险
        trading_frequency = self._calculate_trading_frequency(trades_df)
        
        # 计算价格波动风险
        price_volatility = self._calculate_price_volatility(trades_df)
        
        return {
            'position_concentration': position_concentration,
            'trading_frequency': trading_frequency,
            'price_volatility': price_volatility,
            'portfolio_risk_score': self._calculate_portfolio_risk_score(
                position_concentration, trading_frequency, price_volatility
            )
        }
    
    def _analyze_risk_trends(self, risk_events_df: pd.DataFrame) -> Dict[str, Any]:
        """分析风险趋势"""
        if risk_events_df.empty:
            return {}
        
        # 按日期分组分析趋势
        risk_events_df['date'] = pd.to_datetime(risk_events_df['timestamp']).dt.date
        
        daily_risk_scores = {}
        for date in risk_events_df['date'].unique():
            daily_events = risk_events_df[risk_events_df['date'] == date]
            events_by_level = daily_events['level'].value_counts().to_dict()
            daily_risk_scores[str(date)] = self._calculate_risk_score(events_by_level)
        
        # 计算趋势
        scores = list(daily_risk_scores.values())
        if len(scores) >= 2:
            trend = "上升" if scores[-1] > scores[0] else "下降" if scores[-1] < scores[0] else "平稳"
            trend_strength = abs(scores[-1] - scores[0]) / max(scores) if max(scores) > 0 else 0
        else:
            trend = "无法确定"
            trend_strength = 0
        
        return {
            'daily_risk_scores': daily_risk_scores,
            'trend': trend,
            'trend_strength': trend_strength,
            'avg_risk_score': np.mean(scores) if scores else 0,
            'max_risk_score': max(scores) if scores else 0,
            'min_risk_score': min(scores) if scores else 0
        }
    
    def _generate_risk_recommendations(self, risk_events_df: pd.DataFrame, 
                                     trades_df: pd.DataFrame) -> List[Dict[str, Any]]:
        """生成风险建议"""
        recommendations = []
        
        if not risk_events_df.empty:
            # 基于风险事件的建议
            critical_events = risk_events_df[risk_events_df['level'] == 'critical']
            if not critical_events.empty:
                recommendations.append({
                    'priority': 'high',
                    'type': 'critical_risk',
                    'title': '严重风险事件处理',
                    'description': f'发现{len(critical_events)}个严重风险事件，需要立即处理',
                    'action': '立即检查并处理所有严重风险事件'
                })
            
            # 高频风险事件建议
            event_types = risk_events_df['event_type'].value_counts()
            if len(event_types) > 0:
                most_frequent = event_types.index[0]
                if event_types.iloc[0] > 5:  # 如果某类事件超过5次
                    recommendations.append({
                        'priority': 'medium',
                        'type': 'frequent_risk',
                        'title': '高频风险事件优化',
                        'description': f'"{most_frequent}"类型风险事件发生频率较高({event_types.iloc[0]}次)',
                        'action': f'优化相关风控规则，减少"{most_frequent}"类型风险事件'
                    })
        
        if not trades_df.empty:
            # 基于交易数据的建议
            symbol_trades = trades_df['symbol'].value_counts()
            if len(symbol_trades) > 0 and symbol_trades.iloc[0] > 20:  # 某股票交易次数过多
                recommendations.append({
                    'priority': 'low',
                    'type': 'trading_concentration',
                    'title': '交易集中度风险',
                    'description': f'股票{symbol_trades.index[0]}交易次数过多({symbol_trades.iloc[0]}次)',
                    'action': '考虑分散交易，降低单一股票交易集中度'
                })
        
        # 通用建议
        if not recommendations:
            recommendations.append({
                'priority': 'low',
                'type': 'general',
                'title': '持续监控',
                'description': '当前风险水平较低，建议继续保持现有风控措施',
                'action': '定期检查风控参数，确保风险控制有效性'
            })
        
        return recommendations
    
    def _assess_daily_risk_level(self, risk_events_df: pd.DataFrame, 
                               trades_df: pd.DataFrame) -> str:
        """评估日度风险等级"""
        if risk_events_df.empty:
            return "低风险"
        
        critical_count = len(risk_events_df[risk_events_df['level'] == 'critical'])
        high_count = len(risk_events_df[risk_events_df['level'] == 'high'])
        
        if critical_count > 0:
            return "严重风险"
        elif high_count > 3:
            return "高风险"
        elif len(risk_events_df) > 10:
            return "中等风险"
        else:
            return "低风险"
    
    def _identify_key_risks(self, risk_events_df: pd.DataFrame) -> List[str]:
        """识别关键风险"""
        if risk_events_df.empty:
            return []
        
        key_risks = []
        
        # 严重和高风险事件
        critical_high_events = risk_events_df[
            risk_events_df['level'].isin(['critical', 'high'])
        ]
        
        if not critical_high_events.empty:
            event_types = critical_high_events['event_type'].unique()
            key_risks.extend(event_types)
        
        return list(set(key_risks))[:5]  # 最多返回5个关键风险
    
    def _generate_action_items(self, risk_events_df: pd.DataFrame) -> List[str]:
        """生成行动项"""
        if risk_events_df.empty:
            return ["继续监控市场风险"]
        
        action_items = []
        
        # 基于风险等级生成行动项
        critical_events = risk_events_df[risk_events_df['level'] == 'critical']
        if not critical_events.empty:
            action_items.append("立即处理所有严重风险事件")
        
        high_events = risk_events_df[risk_events_df['level'] == 'high']
        if not high_events.empty:
            action_items.append("优先处理高风险事件")
        
        # 基于事件类型生成行动项
        event_types = risk_events_df['event_type'].value_counts()
        if 'position_limit_exceeded' in event_types.index:
            action_items.append("检查并调整仓位限制参数")
        
        if 'daily_loss_limit_exceeded' in event_types.index:
            action_items.append("评估日度亏损限制设置")
        
        if not action_items:
            action_items.append("继续保持当前风控措施")
        
        return action_items[:5]  # 最多返回5个行动项
    
    def _calculate_risk_score(self, events_by_level: Dict[str, int]) -> float:
        """计算风险评分"""
        weights = {
            'critical': 10,
            'high': 5,
            'medium': 2,
            'low': 1
        }
        
        score = 0
        for level, count in events_by_level.items():
            score += weights.get(level, 0) * count
        
        return min(score, 100)  # 最高100分
    
    def _get_risk_rating(self, risk_score: float) -> str:
        """获取风险评级"""
        if risk_score >= 50:
            return "高风险"
        elif risk_score >= 20:
            return "中等风险"
        elif risk_score >= 5:
            return "低风险"
        else:
            return "极低风险"
    
    def _calculate_position_concentration(self, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """计算持仓集中度"""
        if trades_df.empty:
            return {}
        
        # 按股票计算交易金额
        symbol_amounts = trades_df.groupby('symbol')['amount'].sum()
        total_amount = symbol_amounts.sum()
        
        if total_amount == 0:
            return {}
        
        # 计算集中度指标
        concentrations = symbol_amounts / total_amount
        top_5_concentration = concentrations.nlargest(5).sum()
        herfindahl_index = (concentrations ** 2).sum()
        
        return {
            'top_symbol': symbol_amounts.idxmax(),
            'top_symbol_ratio': concentrations.max(),
            'top_5_concentration': top_5_concentration,
            'herfindahl_index': herfindahl_index,
            'concentration_risk': 'high' if top_5_concentration > 0.8 else 'medium' if top_5_concentration > 0.6 else 'low'
        }
    
    def _calculate_trading_frequency(self, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """计算交易频率"""
        if trades_df.empty:
            return {}
        
        trades_df['date'] = pd.to_datetime(trades_df['timestamp']).dt.date
        daily_trades = trades_df.groupby('date').size()
        
        return {
            'avg_daily_trades': daily_trades.mean(),
            'max_daily_trades': daily_trades.max(),
            'trading_days': len(daily_trades),
            'frequency_risk': 'high' if daily_trades.max() > 50 else 'medium' if daily_trades.max() > 20 else 'low'
        }
    
    def _calculate_price_volatility(self, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """计算价格波动风险"""
        if trades_df.empty:
            return {}
        
        # 按股票计算价格波动
        symbol_volatility = {}
        for symbol in trades_df['symbol'].unique():
            symbol_trades = trades_df[trades_df['symbol'] == symbol]
            if len(symbol_trades) > 1:
                price_std = symbol_trades['price'].std()
                price_mean = symbol_trades['price'].mean()
                cv = price_std / price_mean if price_mean > 0 else 0
                symbol_volatility[symbol] = cv
        
        if not symbol_volatility:
            return {}
        
        avg_volatility = np.mean(list(symbol_volatility.values()))
        max_volatility = max(symbol_volatility.values())
        
        return {
            'avg_volatility': avg_volatility,
            'max_volatility': max_volatility,
            'high_volatility_symbols': [s for s, v in symbol_volatility.items() if v > 0.1],
            'volatility_risk': 'high' if max_volatility > 0.2 else 'medium' if max_volatility > 0.1 else 'low'
        }
    
    def _calculate_portfolio_risk_score(self, concentration: Dict, frequency: Dict, volatility: Dict) -> float:
        """计算投资组合风险评分"""
        score = 0
        
        # 集中度风险评分
        if concentration.get('concentration_risk') == 'high':
            score += 30
        elif concentration.get('concentration_risk') == 'medium':
            score += 15
        
        # 交易频率风险评分
        if frequency.get('frequency_risk') == 'high':
            score += 25
        elif frequency.get('frequency_risk') == 'medium':
            score += 10
        
        # 波动率风险评分
        if volatility.get('volatility_risk') == 'high':
            score += 25
        elif volatility.get('volatility_risk') == 'medium':
            score += 10
        
        return min(score, 100)
    
    def _generate_risk_charts(self, report_data: Dict[str, Any], start_date: str, end_date: str):
        """生成风险图表"""
        try:
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle(f'风险报告图表 ({start_date} 到 {end_date})', fontsize=16)
            
            # 风险事件等级分布
            risk_summary = report_data.get('risk_summary', {})
            events_by_level = risk_summary.get('events_by_level', {})
            if events_by_level:
                levels = list(events_by_level.keys())
                counts = list(events_by_level.values())
                colors = ['red' if l == 'critical' else 'orange' if l == 'high' else 'yellow' if l == 'medium' else 'green' for l in levels]
                axes[0, 0].bar(levels, counts, color=colors)
                axes[0, 0].set_title('风险事件等级分布')
                axes[0, 0].set_ylabel('事件数量')
            else:
                axes[0, 0].text(0.5, 0.5, '无风险事件数据', ha='center', va='center')
                axes[0, 0].set_title('风险事件等级分布')
            
            # 风险趋势
            risk_trends = report_data.get('risk_trends', {})
            daily_scores = risk_trends.get('daily_risk_scores', {})
            if daily_scores:
                dates = list(daily_scores.keys())
                scores = list(daily_scores.values())
                axes[0, 1].plot(dates, scores, marker='o', color='red')
                axes[0, 1].set_title('风险评分趋势')
                axes[0, 1].set_ylabel('风险评分')
                axes[0, 1].tick_params(axis='x', rotation=45)
            else:
                axes[0, 1].text(0.5, 0.5, '无风险趋势数据', ha='center', va='center')
                axes[0, 1].set_title('风险评分趋势')
            
            # 风险事件时间分布
            risk_events_analysis = report_data.get('risk_events_analysis', {})
            hourly_dist = risk_events_analysis.get('hourly_distribution', {})
            if hourly_dist:
                hours = list(hourly_dist.keys())
                counts = list(hourly_dist.values())
                axes[1, 0].bar(hours, counts, color='orange')
                axes[1, 0].set_title('风险事件时间分布')
                axes[1, 0].set_xlabel('小时')
                axes[1, 0].set_ylabel('事件数量')
            else:
                axes[1, 0].text(0.5, 0.5, '无时间分布数据', ha='center', va='center')
                axes[1, 0].set_title('风险事件时间分布')
            
            # 投资组合风险评分
            portfolio_risk = report_data.get('portfolio_risk', {})
            risk_score = portfolio_risk.get('portfolio_risk_score', 0)
            concentration_risk = portfolio_risk.get('position_concentration', {}).get('concentration_risk', 'low')
            frequency_risk = portfolio_risk.get('trading_frequency', {}).get('frequency_risk', 'low')
            volatility_risk = portfolio_risk.get('price_volatility', {}).get('volatility_risk', 'low')
            
            risk_components = ['集中度风险', '交易频率风险', '波动率风险']
            risk_levels = [concentration_risk, frequency_risk, volatility_risk]
            risk_colors = ['red' if r == 'high' else 'orange' if r == 'medium' else 'green' for r in risk_levels]
            
            axes[1, 1].bar(risk_components, [1, 1, 1], color=risk_colors)
            axes[1, 1].set_title(f'投资组合风险分析 (总评分: {risk_score:.1f})')
            axes[1, 1].set_ylabel('风险等级')
            axes[1, 1].tick_params(axis='x', rotation=45)
            
            plt.tight_layout()
            chart_file = os.path.join(
                self.output_dir, 
                f"risk_charts_{start_date}_to_{end_date}.png"
            )
            plt.savefig(chart_file, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"风险图表生成完成: {chart_file}")
            
        except Exception as e:
            logger.error(f"生成风险图表失败: {e}")


# 全局风险报告生成器实例
risk_reporter = RiskReporter()
