"""
策略管理模块
"""
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Type
import pandas as pd
from core.base_strategy import BaseStrategy, Signal, StrategyParams
from core.market_data_manager import market_data_manager, MarketData
from core.trade_manager import trade_manager
from core.risk_manager import risk_manager
from utils.logger import get_logger
from utils.database import db_manager

logger = get_logger("StrategyManager")


class StrategyManager:
    """策略管理器"""
    
    def __init__(self):
        self.strategies: Dict[str, BaseStrategy] = {}
        self.strategy_classes: Dict[str, Type[BaseStrategy]] = {}
        self.is_running = False
        self.main_thread = None
        self.update_interval = 1.0  # 策略更新间隔（秒）
        
        # 性能统计
        self.performance_stats = {
            'total_signals': 0,
            'executed_signals': 0,
            'rejected_signals': 0,
            'last_update_time': None
        }
        
        # 注册回调
        self._register_callbacks()
    
    def _register_callbacks(self):
        """注册回调函数"""
        # 注册交易成交回调
        trade_manager.add_fill_callback(self._on_trade_filled)
        
        # 注册行情数据回调
        # 这里可以根据需要订阅特定股票的行情
    
    def register_strategy_class(self, strategy_class: Type[BaseStrategy]):
        """注册策略类"""
        self.strategy_classes[strategy_class.__name__] = strategy_class
        logger.info(f"注册策略类: {strategy_class.__name__}")
    
    def create_strategy(self, strategy_name: str, class_name: str, params: StrategyParams) -> bool:
        """创建策略实例"""
        if class_name not in self.strategy_classes:
            logger.error(f"未找到策略类: {class_name}")
            return False
        
        if strategy_name in self.strategies:
            logger.warning(f"策略已存在: {strategy_name}")
            return False
        
        try:
            strategy_class = self.strategy_classes[class_name]
            strategy = strategy_class(strategy_name, params)
            self.strategies[strategy_name] = strategy
            
            logger.info(f"创建策略成功: {strategy_name}")
            return True
            
        except Exception as e:
            logger.error(f"创建策略失败: {strategy_name}, 错误: {e}")
            return False
    
    def remove_strategy(self, strategy_name: str) -> bool:
        """移除策略"""
        if strategy_name not in self.strategies:
            logger.warning(f"策略不存在: {strategy_name}")
            return False
        
        strategy = self.strategies[strategy_name]
        strategy.stop()
        del self.strategies[strategy_name]
        
        logger.info(f"移除策略: {strategy_name}")
        return True
    
    def start_strategy(self, strategy_name: str) -> bool:
        """启动策略"""
        if strategy_name not in self.strategies:
            logger.error(f"策略不存在: {strategy_name}")
            return False
        
        strategy = self.strategies[strategy_name]
        success = strategy.start()
        
        if success:
            logger.info(f"策略启动成功: {strategy_name}")
        else:
            logger.error(f"策略启动失败: {strategy_name}")
        
        return success
    
    def stop_strategy(self, strategy_name: str) -> bool:
        """停止策略"""
        if strategy_name not in self.strategies:
            logger.error(f"策略不存在: {strategy_name}")
            return False
        
        strategy = self.strategies[strategy_name]
        strategy.stop()
        logger.info(f"策略已停止: {strategy_name}")
        return True
    
    def pause_strategy(self, strategy_name: str) -> bool:
        """暂停策略"""
        if strategy_name not in self.strategies:
            logger.error(f"策略不存在: {strategy_name}")
            return False
        
        strategy = self.strategies[strategy_name]
        strategy.pause()
        logger.info(f"策略已暂停: {strategy_name}")
        return True
    
    def resume_strategy(self, strategy_name: str) -> bool:
        """恢复策略"""
        if strategy_name not in self.strategies:
            logger.error(f"策略不存在: {strategy_name}")
            return False
        
        strategy = self.strategies[strategy_name]
        strategy.resume()
        logger.info(f"策略已恢复: {strategy_name}")
        return True
    
    def start_all_strategies(self):
        """启动所有策略"""
        for strategy_name in self.strategies:
            self.start_strategy(strategy_name)
    
    def stop_all_strategies(self):
        """停止所有策略"""
        for strategy_name in self.strategies:
            self.stop_strategy(strategy_name)
    
    def get_strategy(self, strategy_name: str) -> Optional[BaseStrategy]:
        """获取策略实例"""
        return self.strategies.get(strategy_name)
    
    def list_strategies(self) -> List[Dict[str, Any]]:
        """列出所有策略"""
        strategies = []
        for name, strategy in self.strategies.items():
            strategies.append({
                'name': name,
                'class_name': strategy.__class__.__name__,
                'is_running': strategy.is_running,
                'positions': len(strategy.positions),
                'signals': len(strategy.signals),
                'performance': strategy.get_performance_summary()
            })
        return strategies
    
    def start(self):
        """启动策略管理器"""
        if self.is_running:
            return
        
        self.is_running = True
        self.main_thread = threading.Thread(target=self._main_loop, daemon=True)
        self.main_thread.start()
        
        logger.info("策略管理器已启动")
    
    def stop(self):
        """停止策略管理器"""
        self.is_running = False
        
        # 停止所有策略
        self.stop_all_strategies()
        
        if self.main_thread:
            self.main_thread.join(timeout=5)
        
        logger.info("策略管理器已停止")
    
    def _main_loop(self):
        """主循环"""
        while self.is_running:
            try:
                start_time = time.time()
                
                # 更新所有运行中的策略
                for strategy in self.strategies.values():
                    if strategy.is_running:
                        self._update_strategy(strategy)
                
                # 处理策略信号
                self._process_strategy_signals()
                
                # 更新性能统计
                self.performance_stats['last_update_time'] = datetime.now()
                
                # 控制更新频率
                elapsed = time.time() - start_time
                sleep_time = max(0, self.update_interval - elapsed)
                time.sleep(sleep_time)
                
            except Exception as e:
                logger.error(f"策略管理器主循环异常: {e}")
                time.sleep(5)
    
    def _update_strategy(self, strategy: BaseStrategy):
        """更新单个策略"""
        try:
            # 更新策略持仓信息
            positions = trade_manager.get_all_positions()
            for symbol, position in positions.items():
                strategy.update_position(
                    symbol=symbol,
                    quantity=position.quantity,
                    avg_cost=position.avg_cost,
                    current_price=position.current_price
                )
            
            # 获取策略关注的股票行情数据
            for symbol in strategy.positions.keys():
                market_data = market_data_manager.get_realtime_data(symbol)
                if market_data:
                    strategy.on_market_data(symbol, market_data.to_dict())
            
            strategy.last_update_time = datetime.now()
            
        except Exception as e:
            logger.error(f"更新策略 {strategy.name} 失败: {e}")
    
    def _process_strategy_signals(self):
        """处理策略信号"""
        for strategy in self.strategies.values():
            if not strategy.is_running:
                continue
            
            # 获取策略生成的信号
            signals = strategy.get_latest_signals()
            
            for signal in signals:
                if strategy.should_process_signal(signal):
                    self._execute_signal(signal, strategy)
            
            # 清空已处理的信号
            strategy.clear_signals()
    
    def _execute_signal(self, signal: Signal, strategy: BaseStrategy):
        """执行交易信号"""
        try:
            self.performance_stats['total_signals'] += 1
            
            # 风控检查
            portfolio = trade_manager.get_all_positions()
            account_info = trade_manager.get_account_info()
            
            risk_passed, risk_events = risk_manager.check_signal(signal, portfolio, account_info)
            
            if not risk_passed:
                logger.warning(f"信号被风控拒绝: {signal.symbol} {signal.action}")
                self.performance_stats['rejected_signals'] += 1
                return
            
            # 提交交易信号
            order_id = trade_manager.submit_signal(signal)
            
            if order_id:
                logger.info(f"信号执行成功: {signal.symbol} {signal.action} 订单ID: {order_id}")
                self.performance_stats['executed_signals'] += 1
                
                # 记录策略日志
                strategy.logger.log_signal(
                    symbol=signal.symbol,
                    signal_type=signal.action,
                    signal_strength=signal.confidence,
                    reason=signal.reason,
                    order_id=order_id
                )
            else:
                logger.error(f"信号执行失败: {signal.symbol} {signal.action}")
                self.performance_stats['rejected_signals'] += 1
                
        except Exception as e:
            logger.error(f"执行信号异常: {e}")
            self.performance_stats['rejected_signals'] += 1
    
    def _on_trade_filled(self, order):
        """交易成交回调"""
        # 通知相关策略
        for strategy in self.strategies.values():
            if strategy.name == order.strategy_name:
                strategy.on_trade_filled(
                    symbol=order.symbol,
                    action=order.action,
                    quantity=order.filled_quantity,
                    price=order.filled_price,
                    order_id=order.order_id
                )
                
                # 更新策略性能统计
                if order.action == 'sell':
                    # 计算盈亏
                    position = strategy.get_position(order.symbol)
                    if position:
                        trade_pnl = (order.filled_price - position.avg_cost) * order.filled_quantity
                        strategy.update_performance_stats(trade_pnl)
                
                break
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        total_signals = self.performance_stats['total_signals']
        executed_signals = self.performance_stats['executed_signals']
        
        return {
            'total_strategies': len(self.strategies),
            'running_strategies': len([s for s in self.strategies.values() if s.is_running]),
            'total_signals': total_signals,
            'executed_signals': executed_signals,
            'rejected_signals': self.performance_stats['rejected_signals'],
            'execution_rate': executed_signals / total_signals if total_signals > 0 else 0,
            'last_update_time': self.performance_stats['last_update_time']
        }
    
    def generate_strategy_report(self, strategy_name: str = None) -> Dict[str, Any]:
        """生成策略报告"""
        if strategy_name:
            strategies = [self.strategies[strategy_name]] if strategy_name in self.strategies else []
        else:
            strategies = list(self.strategies.values())
        
        report = {
            'generated_time': datetime.now(),
            'strategies': []
        }
        
        for strategy in strategies:
            strategy_report = {
                'name': strategy.name,
                'class_name': strategy.__class__.__name__,
                'status': 'running' if strategy.is_running else 'stopped',
                'performance': strategy.get_performance_summary(),
                'positions': [pos.__dict__ for pos in strategy.positions.values()],
                'recent_signals': [signal.__dict__ for signal in strategy.get_latest_signals(10)]
            }
            report['strategies'].append(strategy_report)
        
        return report


# 全局策略管理器实例
strategy_manager = StrategyManager()
