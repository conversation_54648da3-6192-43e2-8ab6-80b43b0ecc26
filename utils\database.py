"""
数据库管理模块
"""
import sqlite3
import os
import json
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from contextlib import contextmanager
from core.config import config_manager
from utils.logger import get_logger

logger = get_logger("Database")


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: Optional[str] = None):
        self.db_path = db_path or config_manager.database.db_path
        self._ensure_db_directory()
        self._init_database()
    
    def _ensure_db_directory(self):
        """确保数据库目录存在"""
        db_dir = os.path.dirname(self.db_path)
        if db_dir:
            os.makedirs(db_dir, exist_ok=True)
    
    def _init_database(self):
        """初始化数据库表"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 创建策略表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS strategies (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    description TEXT,
                    parameters TEXT,  -- JSON格式存储参数
                    status TEXT DEFAULT 'inactive',  -- active, inactive, paused
                    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建交易记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_name TEXT,
                    symbol TEXT NOT NULL,
                    action TEXT NOT NULL,  -- buy, sell
                    quantity INTEGER NOT NULL,
                    price REAL NOT NULL,
                    amount REAL NOT NULL,
                    order_id TEXT,
                    status TEXT DEFAULT 'pending',  -- pending, filled, cancelled, failed
                    commission REAL DEFAULT 0,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (strategy_name) REFERENCES strategies (name)
                )
            ''')
            
            # 创建持仓表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS positions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT UNIQUE NOT NULL,
                    quantity INTEGER NOT NULL,
                    avg_cost REAL NOT NULL,
                    current_price REAL,
                    market_value REAL,
                    unrealized_pnl REAL,
                    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建风控记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS risk_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    event_type TEXT NOT NULL,
                    level TEXT NOT NULL,  -- info, warning, error, critical
                    symbol TEXT,
                    strategy_name TEXT,
                    description TEXT NOT NULL,
                    details TEXT,  -- JSON格式存储详细信息
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建行情数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS market_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    price REAL NOT NULL,
                    volume INTEGER,
                    bid_price REAL,
                    ask_price REAL,
                    high_price REAL,
                    low_price REAL,
                    open_price REAL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建策略性能表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS strategy_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_name TEXT NOT NULL,
                    date DATE NOT NULL,
                    total_return REAL,
                    daily_return REAL,
                    sharpe_ratio REAL,
                    max_drawdown REAL,
                    win_rate REAL,
                    total_trades INTEGER,
                    profit_trades INTEGER,
                    loss_trades INTEGER,
                    UNIQUE(strategy_name, date),
                    FOREIGN KEY (strategy_name) REFERENCES strategies (name)
                )
            ''')
            
            # 创建系统日志表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    level TEXT NOT NULL,
                    module TEXT,
                    message TEXT NOT NULL,
                    details TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            logger.info("数据库初始化完成")
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接（上下文管理器）"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
        try:
            yield conn
        finally:
            conn.close()
    
    # ==================== 策略管理 ====================
    
    def save_strategy(self, name: str, description: str = "", parameters: Dict = None, 
                     status: str = "inactive") -> bool:
        """保存策略信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                params_json = json.dumps(parameters or {})
                
                cursor.execute('''
                    INSERT OR REPLACE INTO strategies 
                    (name, description, parameters, status, updated_time)
                    VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (name, description, params_json, status))
                
                conn.commit()
                logger.info(f"策略 {name} 保存成功")
                return True
        except Exception as e:
            logger.error(f"保存策略 {name} 失败: {e}")
            return False
    
    def get_strategy(self, name: str) -> Optional[Dict]:
        """获取策略信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM strategies WHERE name = ?', (name,))
                row = cursor.fetchone()
                
                if row:
                    return {
                        'id': row['id'],
                        'name': row['name'],
                        'description': row['description'],
                        'parameters': json.loads(row['parameters']),
                        'status': row['status'],
                        'created_time': row['created_time'],
                        'updated_time': row['updated_time']
                    }
                return None
        except Exception as e:
            logger.error(f"获取策略 {name} 失败: {e}")
            return None
    
    def list_strategies(self, status: Optional[str] = None) -> List[Dict]:
        """列出所有策略"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                if status:
                    cursor.execute('SELECT * FROM strategies WHERE status = ? ORDER BY updated_time DESC', (status,))
                else:
                    cursor.execute('SELECT * FROM strategies ORDER BY updated_time DESC')
                
                strategies = []
                for row in cursor.fetchall():
                    strategies.append({
                        'id': row['id'],
                        'name': row['name'],
                        'description': row['description'],
                        'parameters': json.loads(row['parameters']),
                        'status': row['status'],
                        'created_time': row['created_time'],
                        'updated_time': row['updated_time']
                    })
                
                return strategies
        except Exception as e:
            logger.error(f"列出策略失败: {e}")
            return []
    
    def update_strategy_status(self, name: str, status: str) -> bool:
        """更新策略状态"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE strategies 
                    SET status = ?, updated_time = CURRENT_TIMESTAMP 
                    WHERE name = ?
                ''', (status, name))
                
                conn.commit()
                logger.info(f"策略 {name} 状态更新为 {status}")
                return True
        except Exception as e:
            logger.error(f"更新策略 {name} 状态失败: {e}")
            return False
    
    # ==================== 交易记录管理 ====================
    
    def save_trade(self, strategy_name: str, symbol: str, action: str, quantity: int,
                   price: float, order_id: str = None, commission: float = 0) -> bool:
        """保存交易记录"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                amount = quantity * price
                
                cursor.execute('''
                    INSERT INTO trades 
                    (strategy_name, symbol, action, quantity, price, amount, order_id, commission)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (strategy_name, symbol, action, quantity, price, amount, order_id, commission))
                
                conn.commit()
                logger.info(f"交易记录保存成功: {action} {symbol} {quantity}@{price}")
                return True
        except Exception as e:
            logger.error(f"保存交易记录失败: {e}")
            return False
    
    def update_trade_status(self, order_id: str, status: str) -> bool:
        """更新交易状态"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE trades SET status = ? WHERE order_id = ?
                ''', (status, order_id))
                
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"更新交易状态失败: {e}")
            return False
    
    def get_trades(self, strategy_name: str = None, symbol: str = None, 
                   start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """获取交易记录"""
        try:
            with self.get_connection() as conn:
                query = 'SELECT * FROM trades WHERE 1=1'
                params = []
                
                if strategy_name:
                    query += ' AND strategy_name = ?'
                    params.append(strategy_name)
                
                if symbol:
                    query += ' AND symbol = ?'
                    params.append(symbol)
                
                if start_date:
                    query += ' AND timestamp >= ?'
                    params.append(start_date)
                
                if end_date:
                    query += ' AND timestamp <= ?'
                    params.append(end_date)
                
                query += ' ORDER BY timestamp DESC'
                
                return pd.read_sql_query(query, conn, params=params)
        except Exception as e:
            logger.error(f"获取交易记录失败: {e}")
            return pd.DataFrame()


# 全局数据库实例
db_manager = DatabaseManager()
