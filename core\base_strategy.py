"""
策略基类模块
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import pandas as pd
from utils.logger import get_strategy_logger
from utils.database import db_manager


@dataclass
class Signal:
    """交易信号"""
    symbol: str
    action: str  # 'buy', 'sell', 'hold'
    quantity: int
    price: float
    confidence: float  # 信号置信度 0-1
    reason: str
    timestamp: datetime
    metadata: Dict[str, Any] = None


@dataclass
class Position:
    """持仓信息"""
    symbol: str
    quantity: int
    avg_cost: float
    current_price: float
    market_value: float
    unrealized_pnl: float
    unrealized_pnl_ratio: float


@dataclass
class StrategyParams:
    """策略参数基类"""
    name: str
    description: str = ""
    enabled: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self.__dict__
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """从字典创建实例"""
        return cls(**data)


class BaseStrategy(ABC):
    """策略基类"""
    
    def __init__(self, name: str, params: StrategyParams):
        self.name = name
        self.params = params
        self.logger = get_strategy_logger(name)
        self.positions: Dict[str, Position] = {}
        self.signals: List[Signal] = []
        self.is_running = False
        self.last_update_time = None
        
        # 性能统计
        self.total_trades = 0
        self.profit_trades = 0
        self.loss_trades = 0
        self.total_return = 0.0
        self.max_drawdown = 0.0
        
        # 保存策略到数据库
        self._save_to_database()
    
    def _save_to_database(self):
        """保存策略信息到数据库"""
        db_manager.save_strategy(
            name=self.name,
            description=self.params.description,
            parameters=self.params.to_dict(),
            status='inactive'
        )
    
    @abstractmethod
    def initialize(self) -> bool:
        """初始化策略"""
        pass
    
    @abstractmethod
    def generate_signals(self, market_data: Dict[str, Any]) -> List[Signal]:
        """生成交易信号"""
        pass
    
    @abstractmethod
    def on_market_data(self, symbol: str, data: Dict[str, Any]):
        """处理市场数据"""
        pass
    
    @abstractmethod
    def on_trade_filled(self, symbol: str, action: str, quantity: int, price: float, order_id: str):
        """处理成交回报"""
        pass
    
    def start(self) -> bool:
        """启动策略"""
        try:
            if self.initialize():
                self.is_running = True
                db_manager.update_strategy_status(self.name, 'active')
                self.logger.info(f"策略 {self.name} 启动成功")
                return True
            else:
                self.logger.error(f"策略 {self.name} 初始化失败")
                return False
        except Exception as e:
            self.logger.error(f"策略 {self.name} 启动失败: {e}")
            return False
    
    def stop(self):
        """停止策略"""
        self.is_running = False
        db_manager.update_strategy_status(self.name, 'inactive')
        self.logger.info(f"策略 {self.name} 已停止")
    
    def pause(self):
        """暂停策略"""
        self.is_running = False
        db_manager.update_strategy_status(self.name, 'paused')
        self.logger.info(f"策略 {self.name} 已暂停")
    
    def resume(self):
        """恢复策略"""
        self.is_running = True
        db_manager.update_strategy_status(self.name, 'active')
        self.logger.info(f"策略 {self.name} 已恢复")
    
    def update_position(self, symbol: str, quantity: int, avg_cost: float, current_price: float):
        """更新持仓信息"""
        market_value = quantity * current_price
        unrealized_pnl = market_value - (quantity * avg_cost)
        unrealized_pnl_ratio = unrealized_pnl / (quantity * avg_cost) if quantity * avg_cost != 0 else 0
        
        position = Position(
            symbol=symbol,
            quantity=quantity,
            avg_cost=avg_cost,
            current_price=current_price,
            market_value=market_value,
            unrealized_pnl=unrealized_pnl,
            unrealized_pnl_ratio=unrealized_pnl_ratio
        )
        
        self.positions[symbol] = position
        self.logger.debug(f"更新持仓: {symbol} 数量:{quantity} 成本:{avg_cost:.2f} 现价:{current_price:.2f}")
    
    def add_signal(self, signal: Signal):
        """添加交易信号"""
        self.signals.append(signal)
        self.logger.log_signal(
            symbol=signal.symbol,
            signal_type=signal.action,
            signal_strength=signal.confidence,
            reason=signal.reason,
            price=signal.price,
            quantity=signal.quantity
        )
    
    def get_latest_signals(self, count: int = 10) -> List[Signal]:
        """获取最新的交易信号"""
        return self.signals[-count:] if len(self.signals) >= count else self.signals
    
    def clear_signals(self):
        """清空信号列表"""
        self.signals.clear()
    
    def get_position(self, symbol: str) -> Optional[Position]:
        """获取指定股票的持仓"""
        return self.positions.get(symbol)
    
    def get_all_positions(self) -> Dict[str, Position]:
        """获取所有持仓"""
        return self.positions.copy()
    
    def calculate_portfolio_value(self) -> float:
        """计算投资组合总价值"""
        return sum(pos.market_value for pos in self.positions.values())
    
    def calculate_total_pnl(self) -> float:
        """计算总盈亏"""
        return sum(pos.unrealized_pnl for pos in self.positions.values())
    
    def update_performance_stats(self, trade_pnl: float):
        """更新性能统计"""
        self.total_trades += 1
        self.total_return += trade_pnl
        
        if trade_pnl > 0:
            self.profit_trades += 1
        elif trade_pnl < 0:
            self.loss_trades += 1
        
        # 计算胜率
        win_rate = self.profit_trades / self.total_trades if self.total_trades > 0 else 0
        
        self.logger.log_performance(self.name, {
            'total_trades': self.total_trades,
            'profit_trades': self.profit_trades,
            'loss_trades': self.loss_trades,
            'win_rate': win_rate,
            'total_return': self.total_return
        })
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        win_rate = self.profit_trades / self.total_trades if self.total_trades > 0 else 0
        
        return {
            'strategy_name': self.name,
            'total_trades': self.total_trades,
            'profit_trades': self.profit_trades,
            'loss_trades': self.loss_trades,
            'win_rate': win_rate,
            'total_return': self.total_return,
            'max_drawdown': self.max_drawdown,
            'current_positions': len(self.positions),
            'portfolio_value': self.calculate_portfolio_value(),
            'total_pnl': self.calculate_total_pnl()
        }
    
    def validate_signal(self, signal: Signal) -> bool:
        """验证交易信号的有效性"""
        # 基本验证
        if not signal.symbol or signal.quantity <= 0 or signal.price <= 0:
            self.logger.warning(f"无效信号: {signal}")
            return False
        
        if signal.action not in ['buy', 'sell', 'hold']:
            self.logger.warning(f"无效信号动作: {signal.action}")
            return False
        
        if not 0 <= signal.confidence <= 1:
            self.logger.warning(f"无效信号置信度: {signal.confidence}")
            return False
        
        return True
    
    def should_process_signal(self, signal: Signal) -> bool:
        """判断是否应该处理信号"""
        if not self.is_running:
            return False
        
        if not self.validate_signal(signal):
            return False
        
        # 可以在这里添加更多的信号过滤逻辑
        return True
    
    def __str__(self) -> str:
        return f"Strategy({self.name}, running={self.is_running}, positions={len(self.positions)})"
    
    def __repr__(self) -> str:
        return self.__str__()
