"""
量化交易系统主程序
"""
import os
import sys
import time
import signal
from datetime import datetime, time as dt_time
from typing import Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.config import config_manager
from core.strategy_manager import strategy_manager
from core.trade_manager import trade_manager, QMTTradeExecutor
from core.market_data_manager import market_data_manager, QMTMarketDataProvider
from core.risk_manager import risk_manager
from strategies.sell_strategy import SellStrategy, SellStrategyParams
from utils.logger import main_logger
from utils.database import db_manager

# 导入QMT API（需要根据实际QMT安装情况调整）
try:
    from qmt.qmt_api import QMTClient
    QMT_AVAILABLE = True
except ImportError:
    main_logger.warning("QMT API未找到，将使用模拟模式")
    QMT_AVAILABLE = False
    QMTClient = None


class TradingSystem:
    """量化交易系统主类"""
    
    def __init__(self):
        self.qmt_client: Optional[QMTClient] = None
        self.is_running = False
        self.is_trading_time = False
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def initialize(self) -> bool:
        """初始化系统"""
        try:
            main_logger.info("开始初始化量化交易系统...")
            
            # 1. 初始化QMT客户端
            if not self._init_qmt_client():
                return False
            
            # 2. 初始化交易执行器
            if not self._init_trade_executor():
                return False
            
            # 3. 初始化行情数据提供者
            if not self._init_market_data_provider():
                return False
            
            # 4. 注册策略类
            self._register_strategies()
            
            # 5. 创建默认策略
            if not self._create_default_strategies():
                return False
            
            main_logger.info("量化交易系统初始化完成")
            return True
            
        except Exception as e:
            main_logger.error(f"系统初始化失败: {e}")
            return False
    
    def _init_qmt_client(self) -> bool:
        """初始化QMT客户端"""
        if not QMT_AVAILABLE:
            main_logger.warning("QMT不可用，跳过QMT客户端初始化")
            return True
        
        try:
            self.qmt_client = QMTClient(path=config_manager.qmt.path)
            main_logger.info(f"QMT客户端初始化成功: {config_manager.qmt.path}")
            
            # 连接交易模块
            self.qmt_client.connect_trade(
                config_manager.qmt.account_id,
                config_manager.qmt.account_type
            )
            main_logger.info(f"QMT交易模块连接成功: {config_manager.qmt.account_id}")
            
            return True
            
        except Exception as e:
            main_logger.error(f"QMT客户端初始化失败: {e}")
            return False
    
    def _init_trade_executor(self) -> bool:
        """初始化交易执行器"""
        try:
            if self.qmt_client:
                executor = QMTTradeExecutor(
                    self.qmt_client,
                    config_manager.qmt.account_id,
                    config_manager.qmt.account_type
                )
                trade_manager.set_executor(executor)
                
                if trade_manager.connect():
                    main_logger.info("交易执行器初始化成功")
                    return True
                else:
                    main_logger.error("交易执行器连接失败")
                    return False
            else:
                main_logger.warning("QMT客户端不可用，跳过交易执行器初始化")
                return True
                
        except Exception as e:
            main_logger.error(f"交易执行器初始化失败: {e}")
            return False
    
    def _init_market_data_provider(self) -> bool:
        """初始化行情数据提供者"""
        try:
            if self.qmt_client:
                provider = QMTMarketDataProvider(self.qmt_client)
                market_data_manager.add_provider(provider)
                
                if market_data_manager.connect_all_providers():
                    main_logger.info("行情数据提供者初始化成功")
                    return True
                else:
                    main_logger.error("行情数据提供者连接失败")
                    return False
            else:
                main_logger.warning("QMT客户端不可用，跳过行情数据提供者初始化")
                return True
                
        except Exception as e:
            main_logger.error(f"行情数据提供者初始化失败: {e}")
            return False
    
    def _register_strategies(self):
        """注册策略类"""
        strategy_manager.register_strategy_class(SellStrategy)
        main_logger.info("策略类注册完成")
    
    def _create_default_strategies(self) -> bool:
        """创建默认策略"""
        try:
            # 创建卖出策略
            sell_params = SellStrategyParams(
                name="默认卖出策略",
                description="基于止损止盈的卖出策略",
                enabled=True
            )
            
            success = strategy_manager.create_strategy(
                strategy_name="sell_strategy",
                class_name="SellStrategy",
                params=sell_params
            )
            
            if success:
                main_logger.info("默认策略创建成功")
                return True
            else:
                main_logger.error("默认策略创建失败")
                return False
                
        except Exception as e:
            main_logger.error(f"创建默认策略失败: {e}")
            return False
    
    def start(self):
        """启动系统"""
        if self.is_running:
            main_logger.warning("系统已在运行中")
            return
        
        try:
            main_logger.info("启动量化交易系统...")
            
            # 启动各个管理器
            trade_manager.start()
            market_data_manager.start_realtime_updates()
            strategy_manager.start()
            
            # 启动默认策略
            strategy_manager.start_strategy("sell_strategy")
            
            self.is_running = True
            main_logger.info("量化交易系统启动成功")
            
            # 进入主循环
            self._main_loop()
            
        except Exception as e:
            main_logger.error(f"系统启动失败: {e}")
            self.stop()
    
    def stop(self):
        """停止系统"""
        if not self.is_running:
            return
        
        main_logger.info("正在停止量化交易系统...")
        
        try:
            # 停止各个管理器
            strategy_manager.stop()
            market_data_manager.stop_realtime_updates()
            trade_manager.stop()
            
            # 断开连接
            market_data_manager.disconnect_all_providers()
            trade_manager.disconnect()
            
            if self.qmt_client:
                self.qmt_client.disconnect_trade(
                    config_manager.qmt.account_id,
                    config_manager.qmt.account_type
                )
            
            self.is_running = False
            main_logger.info("量化交易系统已停止")
            
        except Exception as e:
            main_logger.error(f"系统停止异常: {e}")
    
    def _main_loop(self):
        """主循环"""
        while self.is_running:
            try:
                current_time = datetime.now().time()
                
                # 检查是否在交易时间
                self._check_trading_time(current_time)
                
                # 在交易时间内执行策略
                if self.is_trading_time:
                    self._run_trading_logic()
                else:
                    # 非交易时间，暂停策略
                    self._pause_strategies()
                
                # 定期输出系统状态
                if datetime.now().minute % 10 == 0:  # 每10分钟输出一次
                    self._log_system_status()
                
                time.sleep(10)  # 10秒检查一次
                
            except KeyboardInterrupt:
                main_logger.info("接收到中断信号，正在停止系统...")
                break
            except Exception as e:
                main_logger.error(f"主循环异常: {e}")
                time.sleep(30)  # 异常时等待30秒
    
    def _check_trading_time(self, current_time: dt_time):
        """检查是否在交易时间"""
        trading_start = dt_time.fromisoformat(config_manager.trading.trading_start_time)
        trading_end = dt_time.fromisoformat(config_manager.trading.trading_end_time)
        lunch_start = dt_time.fromisoformat(config_manager.trading.lunch_break_start)
        lunch_end = dt_time.fromisoformat(config_manager.trading.lunch_break_end)
        
        # 检查是否在交易时间段内
        in_morning_session = trading_start <= current_time < lunch_start
        in_afternoon_session = lunch_end <= current_time < trading_end
        
        new_trading_time = in_morning_session or in_afternoon_session
        
        if new_trading_time != self.is_trading_time:
            self.is_trading_time = new_trading_time
            if self.is_trading_time:
                main_logger.info("进入交易时间")
            else:
                main_logger.info("离开交易时间")
    
    def _run_trading_logic(self):
        """运行交易逻辑"""
        # 恢复暂停的策略
        for strategy_name in strategy_manager.strategies:
            strategy = strategy_manager.get_strategy(strategy_name)
            if strategy and not strategy.is_running:
                strategy_manager.resume_strategy(strategy_name)
    
    def _pause_strategies(self):
        """暂停策略"""
        for strategy_name in strategy_manager.strategies:
            strategy = strategy_manager.get_strategy(strategy_name)
            if strategy and strategy.is_running:
                strategy_manager.pause_strategy(strategy_name)
    
    def _log_system_status(self):
        """记录系统状态"""
        try:
            # 策略状态
            strategy_summary = strategy_manager.get_performance_summary()
            main_logger.info(f"策略状态: {strategy_summary}")
            
            # 交易状态
            trade_stats = trade_manager.get_trade_stats()
            main_logger.info(f"交易统计: {trade_stats}")
            
            # 风控状态
            risk_summary = risk_manager.get_risk_summary()
            main_logger.info(f"风控状态: {risk_summary}")
            
            # 行情状态
            market_quality = market_data_manager.get_data_quality_report()
            main_logger.info(f"行情质量: {market_quality}")
            
        except Exception as e:
            main_logger.error(f"记录系统状态失败: {e}")
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        main_logger.info(f"接收到信号 {signum}，正在停止系统...")
        self.is_running = False


def main():
    """主函数"""
    print("=" * 60)
    print("量化交易系统 v1.0")
    print("=" * 60)
    
    # 创建必要的目录
    os.makedirs("logs", exist_ok=True)
    os.makedirs("data", exist_ok=True)
    os.makedirs("config", exist_ok=True)
    
    # 创建系统实例
    system = TradingSystem()
    
    # 初始化系统
    if not system.initialize():
        main_logger.error("系统初始化失败，退出")
        sys.exit(1)
    
    try:
        # 启动系统
        system.start()
    except KeyboardInterrupt:
        main_logger.info("用户中断，正在停止系统...")
    except Exception as e:
        main_logger.error(f"系统运行异常: {e}")
    finally:
        # 停止系统
        system.stop()
        main_logger.info("系统已退出")


if __name__ == "__main__":
    main()
