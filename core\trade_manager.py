"""
交易管理模块
"""
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum
from queue import Queue, Empty
import pandas as pd
from core.config import config_manager
from core.base_strategy import Signal, Position
from utils.logger import trade_logger
from utils.database import db_manager


class OrderStatus(Enum):
    """订单状态"""
    PENDING = "pending"
    SUBMITTED = "submitted"
    FILLED = "filled"
    PARTIALLY_FILLED = "partially_filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    FAILED = "failed"


class OrderType(Enum):
    """订单类型"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"


@dataclass
class Order:
    """订单信息"""
    order_id: str
    symbol: str
    action: str  # 'buy', 'sell'
    order_type: OrderType
    quantity: int
    price: float
    filled_quantity: int = 0
    filled_price: float = 0.0
    status: OrderStatus = OrderStatus.PENDING
    strategy_name: str = ""
    commission: float = 0.0
    created_time: datetime = None
    updated_time: datetime = None
    error_message: str = ""

    def __post_init__(self):
        if self.created_time is None:
            self.created_time = datetime.now()
        if self.updated_time is None:
            self.updated_time = self.created_time

    @property
    def remaining_quantity(self) -> int:
        """剩余数量"""
        return self.quantity - self.filled_quantity

    @property
    def is_filled(self) -> bool:
        """是否完全成交"""
        return self.filled_quantity >= self.quantity

    @property
    def fill_ratio(self) -> float:
        """成交比例"""
        return self.filled_quantity / self.quantity if self.quantity > 0 else 0

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'order_id': self.order_id,
            'symbol': self.symbol,
            'action': self.action,
            'order_type': self.order_type.value,
            'quantity': self.quantity,
            'price': self.price,
            'filled_quantity': self.filled_quantity,
            'filled_price': self.filled_price,
            'status': self.status.value,
            'strategy_name': self.strategy_name,
            'commission': self.commission,
            'created_time': self.created_time.isoformat() if self.created_time else None,
            'updated_time': self.updated_time.isoformat() if self.updated_time else None,
            'error_message': self.error_message
        }


class TradeExecutor:
    """交易执行器基类"""

    def __init__(self, name: str):
        self.name = name
        self.is_connected = False

    def connect(self) -> bool:
        """连接交易接口"""
        raise NotImplementedError

    def disconnect(self):
        """断开连接"""
        raise NotImplementedError

    def submit_order(self, order: Order) -> bool:
        """提交订单"""
        raise NotImplementedError

    def cancel_order(self, order_id: str) -> bool:
        """撤销订单"""
        raise NotImplementedError

    def query_order(self, order_id: str) -> Optional[Order]:
        """查询订单状态"""
        raise NotImplementedError

    def query_positions(self) -> Dict[str, Position]:
        """查询持仓"""
        raise NotImplementedError

    def query_account(self) -> Dict[str, Any]:
        """查询账户信息"""
        raise NotImplementedError


class QMTTradeExecutor(TradeExecutor):
    """QMT交易执行器"""

    def __init__(self, qmt_client, account_id: str, account_type: str):
        super().__init__("QMT")
        self.qmt_client = qmt_client
        self.account_id = account_id
        self.account_type = account_type

    def connect(self) -> bool:
        """连接QMT交易接口"""
        try:
            self.qmt_client.connect_trade(self.account_id, self.account_type)
            self.is_connected = True
            trade_logger.info(f"QMT交易接口连接成功: {self.account_id}")
            return True
        except Exception as e:
            trade_logger.error(f"QMT交易接口连接失败: {e}")
            return False

    def disconnect(self):
        """断开QMT连接"""
        try:
            self.qmt_client.disconnect_trade(self.account_id, self.account_type)
            self.is_connected = False
            trade_logger.info("QMT交易接口已断开")
        except Exception as e:
            trade_logger.error(f"断开QMT连接失败: {e}")

    def submit_order(self, order: Order) -> bool:
        """提交QMT订单"""
        try:
            # 转换订单类型
            qmt_order_type = 1 if order.order_type == OrderType.LIMIT else 2  # 1-限价, 2-市价
            qmt_direction = 1 if order.action == 'buy' else 2  # 1-买入, 2-卖出

            response = self.qmt_client.send_order(
                account_id=self.account_id,
                account_type=self.account_type,
                stock_code=order.symbol,
                order_type=qmt_order_type,
                price=order.price,
                amount=order.quantity,
                direction=qmt_direction
            )

            if response and response.get('error_code') == 0:
                order.order_id = response.get('order_id', order.order_id)
                order.status = OrderStatus.SUBMITTED
                order.updated_time = datetime.now()

                trade_logger.log_trade(
                    action=order.action,
                    symbol=order.symbol,
                    quantity=order.quantity,
                    price=order.price,
                    order_id=order.order_id
                )
                return True
            else:
                error_msg = response.get('error_msg', '未知错误') if response else 'API调用无响应'
                order.status = OrderStatus.REJECTED
                order.error_message = error_msg
                order.updated_time = datetime.now()

                trade_logger.error(f"订单提交失败: {error_msg}")
                return False

        except Exception as e:
            order.status = OrderStatus.FAILED
            order.error_message = str(e)
            order.updated_time = datetime.now()
            trade_logger.error(f"提交订单异常: {e}")
            return False

    def cancel_order(self, order_id: str) -> bool:
        """撤销QMT订单"""
        try:
            response = self.qmt_client.cancel_order(
                account_id=self.account_id,
                account_type=self.account_type,
                order_id=order_id
            )

            if response and response.get('error_code') == 0:
                trade_logger.info(f"订单撤销成功: {order_id}")
                return True
            else:
                error_msg = response.get('error_msg', '未知错误') if response else 'API调用无响应'
                trade_logger.error(f"订单撤销失败: {error_msg}")
                return False

        except Exception as e:
            trade_logger.error(f"撤销订单异常: {e}")
            return False

    def query_order(self, order_id: str) -> Optional[Order]:
        """查询QMT订单状态"""
        try:
            orders_df = self.qmt_client.query_orders(self.account_id, self.account_type)
            if orders_df is None or orders_df.empty:
                return None

            # 查找指定订单
            order_row = orders_df[orders_df['order_id'] == order_id]
            if order_row.empty:
                return None

            row = order_row.iloc[0]

            # 转换QMT状态到内部状态
            qmt_status = row.get('order_status', '')
            if qmt_status == '已成交':
                status = OrderStatus.FILLED
            elif qmt_status == '部分成交':
                status = OrderStatus.PARTIALLY_FILLED
            elif qmt_status == '已撤销':
                status = OrderStatus.CANCELLED
            elif qmt_status == '已报':
                status = OrderStatus.SUBMITTED
            else:
                status = OrderStatus.PENDING

            order = Order(
                order_id=order_id,
                symbol=row.get('stock_code', ''),
                action='buy' if row.get('direction', 1) == 1 else 'sell',
                order_type=OrderType.LIMIT if row.get('order_type', 1) == 1 else OrderType.MARKET,
                quantity=int(row.get('amount', 0)),
                price=float(row.get('price', 0)),
                filled_quantity=int(row.get('filled_amount', 0)),
                filled_price=float(row.get('filled_price', 0)),
                status=status,
                commission=float(row.get('commission', 0))
            )

            return order

        except Exception as e:
            trade_logger.error(f"查询订单状态异常: {e}")
            return None

    def query_positions(self) -> Dict[str, Position]:
        """查询QMT持仓"""
        try:
            positions_df = self.qmt_client.query_account_positions(self.account_id, self.account_type)
            if positions_df is None or positions_df.empty:
                return {}

            positions = {}
            for index, row in positions_df.iterrows():
                symbol = row['stock_code']
                quantity = int(row['current_amount'])
                avg_cost = float(row['cost_price'])
                current_price = float(row['last_price'])
                market_value = quantity * current_price
                unrealized_pnl = market_value - (quantity * avg_cost)
                unrealized_pnl_ratio = unrealized_pnl / (quantity * avg_cost) if quantity * avg_cost != 0 else 0

                positions[symbol] = Position(
                    symbol=symbol,
                    quantity=quantity,
                    avg_cost=avg_cost,
                    current_price=current_price,
                    market_value=market_value,
                    unrealized_pnl=unrealized_pnl,
                    unrealized_pnl_ratio=unrealized_pnl_ratio
                )

            return positions

        except Exception as e:
            trade_logger.error(f"查询持仓异常: {e}")
            return {}

    def query_account(self) -> Dict[str, Any]:
        """查询QMT账户信息"""
        try:
            account_df = self.qmt_client.query_account_info(self.account_id, self.account_type)
            if account_df is None or account_df.empty:
                return {}

            row = account_df.iloc[0]

            return {
                'account_id': self.account_id,
                'total_value': float(row.get('total_asset', 0)),
                'available_cash': float(row.get('available_cash', 0)),
                'market_value': float(row.get('market_value', 0)),
                'frozen_cash': float(row.get('frozen_cash', 0)),
                'daily_pnl': float(row.get('daily_pnl', 0)),
                'total_pnl': float(row.get('total_pnl', 0))
            }

        except Exception as e:
            trade_logger.error(f"查询账户信息异常: {e}")
            return {}


class TradeManager:
    """交易管理器"""

    def __init__(self):
        self.executor: Optional[TradeExecutor] = None
        self.orders: Dict[str, Order] = {}
        self.positions: Dict[str, Position] = {}
        self.account_info: Dict[str, Any] = {}

        self.order_queue = Queue()
        self.is_running = False
        self.order_thread = None
        self.monitor_thread = None

        # 交易统计
        self.trade_stats = {
            'total_orders': 0,
            'filled_orders': 0,
            'cancelled_orders': 0,
            'rejected_orders': 0,
            'total_commission': 0.0
        }

        # 回调函数
        self.order_callbacks: List[Callable[[Order], None]] = []
        self.fill_callbacks: List[Callable[[Order], None]] = []

    def set_executor(self, executor: TradeExecutor):
        """设置交易执行器"""
        self.executor = executor
        trade_logger.info(f"设置交易执行器: {executor.name}")

    def connect(self) -> bool:
        """连接交易接口"""
        if not self.executor:
            trade_logger.error("未设置交易执行器")
            return False

        return self.executor.connect()

    def disconnect(self):
        """断开交易连接"""
        if self.executor:
            self.executor.disconnect()

    def start(self):
        """启动交易管理器"""
        if self.is_running:
            return

        self.is_running = True

        # 启动订单处理线程
        self.order_thread = threading.Thread(target=self._order_processing_loop, daemon=True)
        self.order_thread.start()

        # 启动订单监控线程
        self.monitor_thread = threading.Thread(target=self._order_monitoring_loop, daemon=True)
        self.monitor_thread.start()

        trade_logger.info("交易管理器已启动")

    def stop(self):
        """停止交易管理器"""
        self.is_running = False

        if self.order_thread:
            self.order_thread.join(timeout=5)

        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)

        trade_logger.info("交易管理器已停止")

    def submit_signal(self, signal: Signal) -> Optional[str]:
        """提交交易信号"""
        if not self.executor or not self.executor.is_connected:
            trade_logger.error("交易执行器未连接")
            return None

        # 生成订单ID
        order_id = f"{signal.symbol}_{signal.action}_{int(time.time() * 1000)}"

        # 创建订单
        order = Order(
            order_id=order_id,
            symbol=signal.symbol,
            action=signal.action,
            order_type=OrderType.LIMIT,  # 默认限价单
            quantity=signal.quantity,
            price=signal.price,
            strategy_name=getattr(signal, 'strategy_name', '')
        )

        # 添加到订单队列
        self.order_queue.put(order)
        trade_logger.info(f"交易信号已提交: {signal.symbol} {signal.action} {signal.quantity}@{signal.price}")

        return order_id

    def cancel_order(self, order_id: str) -> bool:
        """撤销订单"""
        if order_id in self.orders:
            order = self.orders[order_id]
            if order.status in [OrderStatus.PENDING, OrderStatus.SUBMITTED]:
                if self.executor and self.executor.cancel_order(order_id):
                    order.status = OrderStatus.CANCELLED
                    order.updated_time = datetime.now()
                    self._update_trade_stats(order)
                    self._notify_order_callbacks(order)
                    return True
        return False

    def get_order(self, order_id: str) -> Optional[Order]:
        """获取订单信息"""
        return self.orders.get(order_id)

    def get_orders(self, symbol: str = None, status: OrderStatus = None) -> List[Order]:
        """获取订单列表"""
        orders = list(self.orders.values())

        if symbol:
            orders = [o for o in orders if o.symbol == symbol]

        if status:
            orders = [o for o in orders if o.status == status]

        return sorted(orders, key=lambda x: x.created_time, reverse=True)

    def update_positions(self):
        """更新持仓信息"""
        if self.executor and self.executor.is_connected:
            self.positions = self.executor.query_positions()

    def update_account_info(self):
        """更新账户信息"""
        if self.executor and self.executor.is_connected:
            self.account_info = self.executor.query_account()

    def get_position(self, symbol: str) -> Optional[Position]:
        """获取持仓信息"""
        return self.positions.get(symbol)

    def get_all_positions(self) -> Dict[str, Position]:
        """获取所有持仓"""
        return self.positions.copy()

    def get_account_info(self) -> Dict[str, Any]:
        """获取账户信息"""
        return self.account_info.copy()

    def add_order_callback(self, callback: Callable[[Order], None]):
        """添加订单状态变化回调"""
        self.order_callbacks.append(callback)

    def add_fill_callback(self, callback: Callable[[Order], None]):
        """添加成交回调"""
        self.fill_callbacks.append(callback)

    def _order_processing_loop(self):
        """订单处理循环"""
        while self.is_running:
            try:
                # 从队列获取订单
                order = self.order_queue.get(timeout=1)

                # 保存订单
                self.orders[order.order_id] = order

                # 提交订单
                if self.executor and self.executor.submit_order(order):
                    trade_logger.info(f"订单提交成功: {order.order_id}")
                    # 保存到数据库
                    db_manager.save_trade(
                        strategy_name=order.strategy_name,
                        symbol=order.symbol,
                        action=order.action,
                        quantity=order.quantity,
                        price=order.price,
                        order_id=order.order_id
                    )
                else:
                    trade_logger.error(f"订单提交失败: {order.order_id}")

                self._update_trade_stats(order)
                self._notify_order_callbacks(order)

            except Empty:
                continue
            except Exception as e:
                trade_logger.error(f"订单处理异常: {e}")

    def _order_monitoring_loop(self):
        """订单监控循环"""
        while self.is_running:
            try:
                # 监控未完成的订单
                pending_orders = [
                    order for order in self.orders.values()
                    if order.status in [OrderStatus.PENDING, OrderStatus.SUBMITTED, OrderStatus.PARTIALLY_FILLED]
                ]

                for order in pending_orders:
                    if self.executor:
                        updated_order = self.executor.query_order(order.order_id)
                        if updated_order:
                            old_status = order.status
                            old_filled_qty = order.filled_quantity

                            # 更新订单状态
                            order.status = updated_order.status
                            order.filled_quantity = updated_order.filled_quantity
                            order.filled_price = updated_order.filled_price
                            order.commission = updated_order.commission
                            order.updated_time = datetime.now()

                            # 检查是否有新的成交
                            if order.filled_quantity > old_filled_qty:
                                trade_logger.info(f"订单部分成交: {order.order_id} 成交量:{order.filled_quantity}")
                                self._notify_fill_callbacks(order)

                            # 检查状态变化
                            if order.status != old_status:
                                trade_logger.info(f"订单状态变化: {order.order_id} {old_status.value} -> {order.status.value}")
                                self._update_trade_stats(order)
                                self._notify_order_callbacks(order)

                                # 更新数据库
                                db_manager.update_trade_status(order.order_id, order.status.value)

                # 更新持仓和账户信息
                self.update_positions()
                self.update_account_info()

                time.sleep(2)  # 2秒检查一次

            except Exception as e:
                trade_logger.error(f"订单监控异常: {e}")
                time.sleep(5)

    def _update_trade_stats(self, order: Order):
        """更新交易统计"""
        if order.status == OrderStatus.SUBMITTED:
            self.trade_stats['total_orders'] += 1
        elif order.status == OrderStatus.FILLED:
            self.trade_stats['filled_orders'] += 1
            self.trade_stats['total_commission'] += order.commission
        elif order.status == OrderStatus.CANCELLED:
            self.trade_stats['cancelled_orders'] += 1
        elif order.status in [OrderStatus.REJECTED, OrderStatus.FAILED]:
            self.trade_stats['rejected_orders'] += 1

    def _notify_order_callbacks(self, order: Order):
        """通知订单回调"""
        for callback in self.order_callbacks:
            try:
                callback(order)
            except Exception as e:
                trade_logger.error(f"订单回调异常: {e}")

    def _notify_fill_callbacks(self, order: Order):
        """通知成交回调"""
        for callback in self.fill_callbacks:
            try:
                callback(order)
            except Exception as e:
                trade_logger.error(f"成交回调异常: {e}")

    def get_trade_stats(self) -> Dict[str, Any]:
        """获取交易统计"""
        stats = self.trade_stats.copy()
        stats['success_rate'] = (
            stats['filled_orders'] / stats['total_orders']
            if stats['total_orders'] > 0 else 0
        )
        return stats


# 全局交易管理器实例
trade_manager = TradeManager()
