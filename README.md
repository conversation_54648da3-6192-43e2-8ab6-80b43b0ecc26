# 量化交易系统

一个完整的量化交易系统，包含策略管理、风控管理、交易管理和行情管理四个核心模块。

## 系统架构

### 核心模块

1. **策略管理模块** (`core/strategy_manager.py`)
   - 策略开发和管理
   - 策略回测
   - 实时策略执行
   - 信号生成与过滤
   - 策略参数优化
   - 策略报告与分析

2. **风控管理模块** (`core/risk_manager.py`)
   - 交易限制
   - 止损和止盈
   - 风险敞口管理
   - 风险模型评估
   - 杠杆管理
   - 风险报告与监控

3. **交易管理模块** (`core/trade_manager.py`)
   - 订单管理
   - 执行交易指令
   - 交易监控与跟踪
   - 交易费用计算
   - 交易执行日志

4. **行情管理模块** (`core/market_data_manager.py`)
   - 行情数据获取
   - 行情数据存储与管理
   - 行情数据分析与处理
   - 实时行情监控与订阅
   - 行情数据质量监控
   - 行情数据可视化

### 支持模块

- **配置管理** (`core/config.py`) - 系统配置管理
- **日志管理** (`utils/logger.py`) - 日志记录和管理
- **数据库管理** (`utils/database.py`) - 数据存储和查询
- **策略基类** (`core/base_strategy.py`) - 策略开发框架

## 功能特性

### 策略管理
- ✅ 策略开发框架
- ✅ 策略生命周期管理
- ✅ 信号生成和验证
- ✅ 策略性能统计
- ✅ 策略报告生成

### 风控管理
- ✅ 多层次风险检查
- ✅ 实时风险监控
- ✅ 风险事件记录
- ✅ 风险限制管理
- ✅ 风险报告生成

### 交易管理
- ✅ 订单生命周期管理
- ✅ 多种订单类型支持
- ✅ 交易状态监控
- ✅ 成交回报处理
- ✅ 交易统计分析

### 行情管理
- ✅ 实时行情订阅
- ✅ 行情数据缓存
- ✅ 数据质量监控
- ✅ 多数据源支持
- ✅ 历史数据查询

## 安装和配置

### 环境要求

- Python 3.8+
- pandas >= 1.5.0
- numpy >= 1.21.0
- matplotlib >= 3.5.0
- seaborn >= 0.11.0

### 安装依赖

```bash
pip install -r requirements.txt
```

### 配置系统

1. 修改 `core/config.py` 中的QMT配置：
```python
QMT_PATH = "D:\\恒泰证券迅投QMT"  # 您的QMT安装路径
ACCOUNT_ID = "YOUR_ACCOUNT_ID"   # 您的资金账号
ACCOUNT_TYPE = "STOCK"           # 账户类型
```

2. 创建必要的目录：
```bash
mkdir logs data config reports
```

## 使用方法

### 1. 启动主系统

```bash
python main.py
```

### 2. 运行回测

```bash
python backtest.py
```

### 3. 生成报告

```python
from reports.strategy_reporter import strategy_reporter
from reports.risk_reporter import risk_reporter

# 生成策略报告
strategy_reporter.generate_daily_report()
strategy_reporter.generate_strategy_report("strategy_name")

# 生成风险报告
risk_reporter.generate_risk_report()
risk_reporter.generate_daily_risk_summary()
```

## 策略开发

### 创建自定义策略

1. 继承 `BaseStrategy` 类：

```python
from core.base_strategy import BaseStrategy, Signal, StrategyParams

class MyStrategy(BaseStrategy):
    def initialize(self) -> bool:
        # 策略初始化逻辑
        return True
    
    def generate_signals(self, market_data: Dict[str, Any]) -> List[Signal]:
        # 信号生成逻辑
        signals = []
        # ... 策略逻辑
        return signals
    
    def on_market_data(self, symbol: str, data: Dict[str, Any]):
        # 行情数据处理
        pass
    
    def on_trade_filled(self, symbol: str, action: str, quantity: int, price: float, order_id: str):
        # 成交回报处理
        pass
```

2. 注册策略：

```python
from core.strategy_manager import strategy_manager

strategy_manager.register_strategy_class(MyStrategy)
```

### 内置策略

系统提供了以下内置策略：

1. **卖出策略** (`strategies/sell_strategy.py`)
   - 基于止损止盈的卖出策略
   - 支持百分比止损和绝对价格止损
   - 可配置卖出数量和订单类型

2. **MACD策略** (`strategies/sample_strategies.py`)
   - 基于MACD指标的金叉死叉策略
   - 支持参数自定义

3. **移动平均策略** (`strategies/sample_strategies.py`)
   - 基于双均线的金叉死叉策略
   - 支持短期和长期均线参数配置

## 风险管理

### 风险检查器

系统内置多种风险检查器：

- **仓位大小检查** - 控制单只股票最大仓位比例
- **日度亏损检查** - 控制单日最大亏损
- **止损检查** - 监控止损触发
- **订单金额检查** - 控制单笔订单最大金额

### 自定义风险检查器

```python
from core.risk_manager import RiskChecker, risk_manager

class MyRiskChecker(RiskChecker):
    def check(self, signal, portfolio, account_info):
        # 自定义风险检查逻辑
        return True, None  # (是否通过, 风险事件)

risk_manager.add_checker(MyRiskChecker("MyChecker"))
```

## 数据管理

### 数据库结构

系统使用SQLite数据库存储以下数据：

- 策略信息
- 交易记录
- 持仓信息
- 风险事件
- 行情数据
- 策略性能
- 系统日志

### 数据查询

```python
from utils.database import db_manager

# 获取交易记录
trades = db_manager.get_trades(strategy_name="my_strategy")

# 获取策略信息
strategy = db_manager.get_strategy("strategy_name")
```

## 报告系统

### 策略报告

- 日度报告
- 策略性能报告
- 交易分析报告
- 持仓分析报告

### 风险报告

- 风险事件分析
- 投资组合风险评估
- 风险趋势分析
- 风险建议

## 监控和日志

### 日志级别

- DEBUG - 调试信息
- INFO - 一般信息
- WARNING - 警告信息
- ERROR - 错误信息
- CRITICAL - 严重错误

### 日志配置

在 `core/config.py` 中配置日志参数：

```python
log_level = "INFO"
log_file = "logs/trading_system.log"
max_file_size = 10 * 1024 * 1024  # 10MB
backup_count = 5
```

## 注意事项

1. **QMT依赖** - 系统依赖QMT客户端，请确保正确安装和配置
2. **数据安全** - 请妥善保管账户信息和交易数据
3. **风险控制** - 请根据实际情况调整风控参数
4. **测试环境** - 建议先在模拟环境中测试策略
5. **监控运行** - 请密切监控系统运行状态

## 故障排除

### 常见问题

1. **QMT连接失败**
   - 检查QMT客户端是否正常运行
   - 确认账户信息配置正确
   - 检查网络连接

2. **策略不执行**
   - 检查策略是否正确启动
   - 确认交易时间设置
   - 查看日志文件排查错误

3. **数据库错误**
   - 检查数据库文件权限
   - 确认磁盘空间充足
   - 查看错误日志

### 日志查看

```bash
# 查看主日志
tail -f logs/trading_system.log

# 查看特定模块日志
grep "StrategyManager" logs/trading_system.log
```

## 贡献

欢迎提交Issue和Pull Request来改进系统。

## 许可证

本项目采用MIT许可证。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交GitHub Issue
- 发送邮件至开发团队

---

**免责声明**: 本系统仅供学习和研究使用，实际交易请谨慎操作，投资有风险。
