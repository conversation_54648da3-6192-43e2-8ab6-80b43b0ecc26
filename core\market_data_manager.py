"""
行情管理模块
"""
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from queue import Queue, Empty
import pandas as pd
from utils.logger import get_logger
from utils.database import db_manager

logger = get_logger("MarketData")


@dataclass
class MarketData:
    """市场行情数据"""
    symbol: str
    price: float
    volume: int = 0
    bid_price: float = 0.0
    ask_price: float = 0.0
    bid_volume: int = 0
    ask_volume: int = 0
    high_price: float = 0.0
    low_price: float = 0.0
    open_price: float = 0.0
    prev_close: float = 0.0
    change: float = 0.0
    change_ratio: float = 0.0
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        
        # 计算涨跌幅
        if self.prev_close > 0:
            self.change = self.price - self.prev_close
            self.change_ratio = self.change / self.prev_close
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'symbol': self.symbol,
            'price': self.price,
            'volume': self.volume,
            'bid_price': self.bid_price,
            'ask_price': self.ask_price,
            'bid_volume': self.bid_volume,
            'ask_volume': self.ask_volume,
            'high_price': self.high_price,
            'low_price': self.low_price,
            'open_price': self.open_price,
            'prev_close': self.prev_close,
            'change': self.change,
            'change_ratio': self.change_ratio,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None
        }


class MarketDataProvider:
    """行情数据提供者基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.is_connected = False
        self.subscribed_symbols = set()
    
    def connect(self) -> bool:
        """连接行情源"""
        raise NotImplementedError
    
    def disconnect(self):
        """断开连接"""
        raise NotImplementedError
    
    def subscribe(self, symbols: List[str]) -> bool:
        """订阅行情"""
        raise NotImplementedError
    
    def unsubscribe(self, symbols: List[str]) -> bool:
        """取消订阅"""
        raise NotImplementedError
    
    def get_realtime_data(self, symbol: str) -> Optional[MarketData]:
        """获取实时行情"""
        raise NotImplementedError
    
    def get_historical_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取历史行情"""
        raise NotImplementedError


class QMTMarketDataProvider(MarketDataProvider):
    """QMT行情数据提供者"""
    
    def __init__(self, qmt_client):
        super().__init__("QMT")
        self.qmt_client = qmt_client
    
    def connect(self) -> bool:
        """连接QMT行情"""
        try:
            # QMT客户端通常在初始化时就已连接
            self.is_connected = True
            logger.info("QMT行情连接成功")
            return True
        except Exception as e:
            logger.error(f"QMT行情连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开QMT连接"""
        self.is_connected = False
        logger.info("QMT行情连接已断开")
    
    def subscribe(self, symbols: List[str]) -> bool:
        """订阅QMT行情"""
        try:
            self.subscribed_symbols.update(symbols)
            logger.info(f"订阅行情成功: {symbols}")
            return True
        except Exception as e:
            logger.error(f"订阅行情失败: {e}")
            return False
    
    def unsubscribe(self, symbols: List[str]) -> bool:
        """取消订阅QMT行情"""
        try:
            self.subscribed_symbols.difference_update(symbols)
            logger.info(f"取消订阅行情: {symbols}")
            return True
        except Exception as e:
            logger.error(f"取消订阅行情失败: {e}")
            return False
    
    def get_realtime_data(self, symbol: str) -> Optional[MarketData]:
        """获取QMT实时行情"""
        try:
            quote_df = self.qmt_client.query_realtime_quotes([symbol])
            if quote_df is None or quote_df.empty:
                return None
            
            row = quote_df.iloc[0]
            
            market_data = MarketData(
                symbol=symbol,
                price=float(row.get('last_price', 0)),
                volume=int(row.get('volume', 0)),
                bid_price=float(row.get('bid_price1', 0)),
                ask_price=float(row.get('ask_price1', 0)),
                bid_volume=int(row.get('bid_volume1', 0)),
                ask_volume=int(row.get('ask_volume1', 0)),
                high_price=float(row.get('high_price', 0)),
                low_price=float(row.get('low_price', 0)),
                open_price=float(row.get('open_price', 0)),
                prev_close=float(row.get('prev_close', 0))
            )
            
            return market_data
            
        except Exception as e:
            logger.error(f"获取 {symbol} 实时行情失败: {e}")
            return None
    
    def get_historical_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取QMT历史行情"""
        try:
            # 这里需要根据QMT API的具体实现来调整
            # 假设QMT有获取历史数据的方法
            hist_data = self.qmt_client.query_history_data(
                symbol=symbol,
                start_date=start_date,
                end_date=end_date,
                frequency='1d'  # 日线数据
            )
            return hist_data
        except Exception as e:
            logger.error(f"获取 {symbol} 历史行情失败: {e}")
            return pd.DataFrame()


class MarketDataManager:
    """行情数据管理器"""
    
    def __init__(self):
        self.providers: Dict[str, MarketDataProvider] = {}
        self.subscribers: Dict[str, List[Callable]] = {}  # symbol -> [callback_functions]
        self.data_cache: Dict[str, MarketData] = {}
        self.data_queue = Queue()
        self.is_running = False
        self.update_thread = None
        self.cache_duration = 5  # 缓存有效期（秒）
        
        # 数据质量监控
        self.data_quality_stats = {
            'total_updates': 0,
            'failed_updates': 0,
            'delayed_updates': 0,
            'last_update_time': None
        }
    
    def add_provider(self, provider: MarketDataProvider):
        """添加行情数据提供者"""
        self.providers[provider.name] = provider
        logger.info(f"添加行情提供者: {provider.name}")
    
    def remove_provider(self, provider_name: str):
        """移除行情数据提供者"""
        if provider_name in self.providers:
            self.providers[provider_name].disconnect()
            del self.providers[provider_name]
            logger.info(f"移除行情提供者: {provider_name}")
    
    def connect_all_providers(self) -> bool:
        """连接所有行情提供者"""
        success = True
        for provider in self.providers.values():
            if not provider.connect():
                success = False
        return success
    
    def disconnect_all_providers(self):
        """断开所有行情提供者"""
        for provider in self.providers.values():
            provider.disconnect()
    
    def subscribe_symbol(self, symbol: str, callback: Callable[[MarketData], None]):
        """订阅股票行情"""
        if symbol not in self.subscribers:
            self.subscribers[symbol] = []
            # 向所有提供者订阅
            for provider in self.providers.values():
                provider.subscribe([symbol])
        
        self.subscribers[symbol].append(callback)
        logger.info(f"订阅行情: {symbol}")
    
    def unsubscribe_symbol(self, symbol: str, callback: Callable[[MarketData], None] = None):
        """取消订阅股票行情"""
        if symbol in self.subscribers:
            if callback:
                if callback in self.subscribers[symbol]:
                    self.subscribers[symbol].remove(callback)
            else:
                self.subscribers[symbol].clear()
            
            # 如果没有订阅者了，从提供者取消订阅
            if not self.subscribers[symbol]:
                del self.subscribers[symbol]
                for provider in self.providers.values():
                    provider.unsubscribe([symbol])
                
                logger.info(f"取消订阅行情: {symbol}")
    
    def get_realtime_data(self, symbol: str, use_cache: bool = True) -> Optional[MarketData]:
        """获取实时行情数据"""
        # 检查缓存
        if use_cache and symbol in self.data_cache:
            cached_data = self.data_cache[symbol]
            if (datetime.now() - cached_data.timestamp).seconds < self.cache_duration:
                return cached_data
        
        # 从提供者获取数据
        for provider in self.providers.values():
            if provider.is_connected:
                data = provider.get_realtime_data(symbol)
                if data:
                    self.data_cache[symbol] = data
                    self._save_to_database(data)
                    return data
        
        logger.warning(f"无法获取 {symbol} 的实时行情")
        return None
    
    def get_historical_data(self, symbol: str, start_date: str, end_date: str, 
                          provider_name: str = None) -> pd.DataFrame:
        """获取历史行情数据"""
        if provider_name and provider_name in self.providers:
            providers = [self.providers[provider_name]]
        else:
            providers = list(self.providers.values())
        
        for provider in providers:
            if provider.is_connected:
                data = provider.get_historical_data(symbol, start_date, end_date)
                if not data.empty:
                    return data
        
        logger.warning(f"无法获取 {symbol} 的历史行情")
        return pd.DataFrame()
    
    def start_realtime_updates(self):
        """启动实时行情更新"""
        if self.is_running:
            return
        
        self.is_running = True
        self.update_thread = threading.Thread(target=self._update_loop, daemon=True)
        self.update_thread.start()
        logger.info("实时行情更新已启动")
    
    def stop_realtime_updates(self):
        """停止实时行情更新"""
        self.is_running = False
        if self.update_thread:
            self.update_thread.join(timeout=5)
        logger.info("实时行情更新已停止")
    
    def _update_loop(self):
        """行情更新循环"""
        while self.is_running:
            try:
                # 更新所有订阅的股票行情
                for symbol in list(self.subscribers.keys()):
                    data = self.get_realtime_data(symbol, use_cache=False)
                    if data:
                        self._notify_subscribers(symbol, data)
                        self.data_quality_stats['total_updates'] += 1
                        self.data_quality_stats['last_update_time'] = datetime.now()
                    else:
                        self.data_quality_stats['failed_updates'] += 1
                
                time.sleep(1)  # 1秒更新一次
                
            except Exception as e:
                logger.error(f"行情更新循环错误: {e}")
                self.data_quality_stats['failed_updates'] += 1
                time.sleep(5)  # 错误时等待5秒
    
    def _notify_subscribers(self, symbol: str, data: MarketData):
        """通知订阅者"""
        if symbol in self.subscribers:
            for callback in self.subscribers[symbol]:
                try:
                    callback(data)
                except Exception as e:
                    logger.error(f"通知订阅者失败: {e}")
    
    def _save_to_database(self, data: MarketData):
        """保存行情数据到数据库"""
        try:
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO market_data 
                    (symbol, price, volume, bid_price, ask_price, high_price, low_price, open_price)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    data.symbol, data.price, data.volume, data.bid_price,
                    data.ask_price, data.high_price, data.low_price, data.open_price
                ))
                conn.commit()
        except Exception as e:
            logger.error(f"保存行情数据失败: {e}")
    
    def get_data_quality_report(self) -> Dict[str, Any]:
        """获取数据质量报告"""
        total = self.data_quality_stats['total_updates']
        failed = self.data_quality_stats['failed_updates']
        success_rate = (total - failed) / total if total > 0 else 0
        
        return {
            'total_updates': total,
            'failed_updates': failed,
            'success_rate': success_rate,
            'delayed_updates': self.data_quality_stats['delayed_updates'],
            'last_update_time': self.data_quality_stats['last_update_time'],
            'active_subscriptions': len(self.subscribers),
            'cache_size': len(self.data_cache)
        }


# 全局行情管理器实例
market_data_manager = MarketDataManager()
