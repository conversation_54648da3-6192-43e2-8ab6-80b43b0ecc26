"""
风控管理模块
"""
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import pandas as pd
from core.config import config_manager
from core.base_strategy import Signal, Position
from utils.logger import risk_logger
from utils.database import db_manager


class RiskLevel(Enum):
    """风险等级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class RiskEvent:
    """风险事件"""
    event_type: str
    level: RiskLevel
    symbol: str = None
    strategy_name: str = None
    description: str = ""
    details: Dict[str, Any] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.details is None:
            self.details = {}


@dataclass
class RiskLimits:
    """风险限制"""
    max_position_ratio: float = 0.3  # 单只股票最大仓位比例
    max_daily_loss: float = 0.05  # 单日最大亏损比例
    max_total_loss: float = 0.20  # 总最大亏损比例
    stop_loss_ratio: float = 0.02  # 止损比例
    take_profit_ratio: float = 0.05  # 止盈比例
    max_leverage: float = 1.0  # 最大杠杆倍数
    max_order_amount: float = 100000  # 单笔订单最大金额
    max_daily_trades: int = 100  # 单日最大交易次数
    
    def to_dict(self) -> Dict[str, Any]:
        return self.__dict__


class RiskChecker:
    """风险检查器基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.enabled = True
    
    def check(self, signal: Signal, portfolio: Dict[str, Position], 
              account_info: Dict[str, Any]) -> Tuple[bool, Optional[RiskEvent]]:
        """检查风险，返回(是否通过, 风险事件)"""
        raise NotImplementedError


class PositionSizeChecker(RiskChecker):
    """仓位大小检查器"""
    
    def __init__(self, max_position_ratio: float = 0.3):
        super().__init__("PositionSize")
        self.max_position_ratio = max_position_ratio
    
    def check(self, signal: Signal, portfolio: Dict[str, Position], 
              account_info: Dict[str, Any]) -> Tuple[bool, Optional[RiskEvent]]:
        
        if signal.action != 'buy':
            return True, None
        
        total_value = account_info.get('total_value', 0)
        if total_value <= 0:
            return False, RiskEvent(
                event_type="position_check",
                level=RiskLevel.HIGH,
                symbol=signal.symbol,
                description="无法获取账户总价值"
            )
        
        order_value = signal.quantity * signal.price
        current_position = portfolio.get(signal.symbol)
        current_value = current_position.market_value if current_position else 0
        
        new_position_ratio = (current_value + order_value) / total_value
        
        if new_position_ratio > self.max_position_ratio:
            return False, RiskEvent(
                event_type="position_limit_exceeded",
                level=RiskLevel.HIGH,
                symbol=signal.symbol,
                description=f"仓位比例超限: {new_position_ratio:.2%} > {self.max_position_ratio:.2%}",
                details={
                    'current_ratio': new_position_ratio,
                    'limit_ratio': self.max_position_ratio,
                    'order_value': order_value,
                    'current_value': current_value
                }
            )
        
        return True, None


class DailyLossChecker(RiskChecker):
    """单日亏损检查器"""
    
    def __init__(self, max_daily_loss: float = 0.05):
        super().__init__("DailyLoss")
        self.max_daily_loss = max_daily_loss
    
    def check(self, signal: Signal, portfolio: Dict[str, Position], 
              account_info: Dict[str, Any]) -> Tuple[bool, Optional[RiskEvent]]:
        
        today = datetime.now().date()
        daily_pnl = account_info.get('daily_pnl', 0)
        total_value = account_info.get('total_value', 0)
        
        if total_value <= 0:
            return True, None
        
        daily_loss_ratio = abs(daily_pnl) / total_value if daily_pnl < 0 else 0
        
        if daily_loss_ratio > self.max_daily_loss:
            return False, RiskEvent(
                event_type="daily_loss_limit_exceeded",
                level=RiskLevel.CRITICAL,
                description=f"单日亏损超限: {daily_loss_ratio:.2%} > {self.max_daily_loss:.2%}",
                details={
                    'daily_pnl': daily_pnl,
                    'daily_loss_ratio': daily_loss_ratio,
                    'limit_ratio': self.max_daily_loss,
                    'total_value': total_value
                }
            )
        
        return True, None


class StopLossChecker(RiskChecker):
    """止损检查器"""
    
    def __init__(self, default_stop_loss_ratio: float = 0.02):
        super().__init__("StopLoss")
        self.default_stop_loss_ratio = default_stop_loss_ratio
    
    def check(self, signal: Signal, portfolio: Dict[str, Position], 
              account_info: Dict[str, Any]) -> Tuple[bool, Optional[RiskEvent]]:
        
        if signal.action != 'sell':
            return True, None
        
        position = portfolio.get(signal.symbol)
        if not position:
            return True, None
        
        loss_ratio = (position.avg_cost - signal.price) / position.avg_cost
        
        if loss_ratio > self.default_stop_loss_ratio:
            # 这是一个止损信号，记录但允许执行
            return True, RiskEvent(
                event_type="stop_loss_triggered",
                level=RiskLevel.MEDIUM,
                symbol=signal.symbol,
                description=f"触发止损: 亏损比例 {loss_ratio:.2%}",
                details={
                    'avg_cost': position.avg_cost,
                    'current_price': signal.price,
                    'loss_ratio': loss_ratio,
                    'stop_loss_ratio': self.default_stop_loss_ratio
                }
            )
        
        return True, None


class OrderAmountChecker(RiskChecker):
    """订单金额检查器"""
    
    def __init__(self, max_order_amount: float = 100000):
        super().__init__("OrderAmount")
        self.max_order_amount = max_order_amount
    
    def check(self, signal: Signal, portfolio: Dict[str, Position], 
              account_info: Dict[str, Any]) -> Tuple[bool, Optional[RiskEvent]]:
        
        order_amount = signal.quantity * signal.price
        
        if order_amount > self.max_order_amount:
            return False, RiskEvent(
                event_type="order_amount_exceeded",
                level=RiskLevel.HIGH,
                symbol=signal.symbol,
                description=f"订单金额超限: {order_amount:.2f} > {self.max_order_amount:.2f}",
                details={
                    'order_amount': order_amount,
                    'max_amount': self.max_order_amount,
                    'quantity': signal.quantity,
                    'price': signal.price
                }
            )
        
        return True, None


class RiskManager:
    """风控管理器"""
    
    def __init__(self):
        self.risk_limits = RiskLimits(
            max_position_ratio=config_manager.risk.max_position_ratio,
            max_daily_loss=config_manager.risk.max_daily_loss,
            max_total_loss=config_manager.risk.max_total_loss,
            stop_loss_ratio=config_manager.risk.stop_loss_ratio,
            take_profit_ratio=config_manager.risk.take_profit_ratio,
            max_leverage=config_manager.risk.max_leverage
        )
        
        self.checkers: List[RiskChecker] = []
        self.risk_events: List[RiskEvent] = []
        self.enabled = True
        
        # 初始化默认检查器
        self._init_default_checkers()
    
    def _init_default_checkers(self):
        """初始化默认风险检查器"""
        self.checkers = [
            PositionSizeChecker(self.risk_limits.max_position_ratio),
            DailyLossChecker(self.risk_limits.max_daily_loss),
            StopLossChecker(self.risk_limits.stop_loss_ratio),
            OrderAmountChecker(self.risk_limits.max_order_amount)
        ]
    
    def add_checker(self, checker: RiskChecker):
        """添加风险检查器"""
        self.checkers.append(checker)
        risk_logger.info(f"添加风险检查器: {checker.name}")
    
    def remove_checker(self, checker_name: str):
        """移除风险检查器"""
        self.checkers = [c for c in self.checkers if c.name != checker_name]
        risk_logger.info(f"移除风险检查器: {checker_name}")
    
    def check_signal(self, signal: Signal, portfolio: Dict[str, Position], 
                    account_info: Dict[str, Any]) -> Tuple[bool, List[RiskEvent]]:
        """检查交易信号的风险"""
        if not self.enabled:
            return True, []
        
        events = []
        passed = True
        
        for checker in self.checkers:
            if not checker.enabled:
                continue
            
            try:
                check_passed, event = checker.check(signal, portfolio, account_info)
                
                if event:
                    events.append(event)
                    self._save_risk_event(event)
                
                if not check_passed:
                    passed = False
                    risk_logger.log_risk_check(
                        check_type=checker.name,
                        result=False,
                        details=event.description if event else "检查失败",
                        symbol=signal.symbol
                    )
                
            except Exception as e:
                risk_logger.error(f"风险检查器 {checker.name} 执行失败: {e}")
                # 检查器失败时，为安全起见拒绝信号
                passed = False
                events.append(RiskEvent(
                    event_type="checker_error",
                    level=RiskLevel.HIGH,
                    symbol=signal.symbol,
                    description=f"风险检查器 {checker.name} 执行失败: {e}"
                ))
        
        return passed, events
    
    def check_portfolio_risk(self, portfolio: Dict[str, Position], 
                           account_info: Dict[str, Any]) -> List[RiskEvent]:
        """检查投资组合风险"""
        events = []
        
        # 检查总亏损
        total_pnl = sum(pos.unrealized_pnl for pos in portfolio.values())
        total_value = account_info.get('total_value', 0)
        
        if total_value > 0:
            total_loss_ratio = abs(total_pnl) / total_value if total_pnl < 0 else 0
            
            if total_loss_ratio > self.risk_limits.max_total_loss:
                events.append(RiskEvent(
                    event_type="total_loss_limit_exceeded",
                    level=RiskLevel.CRITICAL,
                    description=f"总亏损超限: {total_loss_ratio:.2%} > {self.risk_limits.max_total_loss:.2%}",
                    details={
                        'total_pnl': total_pnl,
                        'total_loss_ratio': total_loss_ratio,
                        'limit_ratio': self.risk_limits.max_total_loss
                    }
                ))
        
        # 检查单只股票仓位集中度
        for symbol, position in portfolio.items():
            if total_value > 0:
                position_ratio = position.market_value / total_value
                if position_ratio > self.risk_limits.max_position_ratio:
                    events.append(RiskEvent(
                        event_type="position_concentration_risk",
                        level=RiskLevel.MEDIUM,
                        symbol=symbol,
                        description=f"仓位集中度过高: {position_ratio:.2%}",
                        details={
                            'position_ratio': position_ratio,
                            'limit_ratio': self.risk_limits.max_position_ratio,
                            'market_value': position.market_value
                        }
                    ))
        
        # 保存风险事件
        for event in events:
            self._save_risk_event(event)
        
        return events
    
    def _save_risk_event(self, event: RiskEvent):
        """保存风险事件到数据库"""
        try:
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO risk_events 
                    (event_type, level, symbol, strategy_name, description, details)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    event.event_type,
                    event.level.value,
                    event.symbol,
                    event.strategy_name,
                    event.description,
                    json.dumps(event.details) if event.details else None
                ))
                conn.commit()
        except Exception as e:
            risk_logger.error(f"保存风险事件失败: {e}")
    
    def get_risk_events(self, start_date: str = None, end_date: str = None, 
                       level: RiskLevel = None) -> List[RiskEvent]:
        """获取风险事件"""
        try:
            with db_manager.get_connection() as conn:
                query = 'SELECT * FROM risk_events WHERE 1=1'
                params = []
                
                if start_date:
                    query += ' AND timestamp >= ?'
                    params.append(start_date)
                
                if end_date:
                    query += ' AND timestamp <= ?'
                    params.append(end_date)
                
                if level:
                    query += ' AND level = ?'
                    params.append(level.value)
                
                query += ' ORDER BY timestamp DESC'
                
                cursor = conn.cursor()
                cursor.execute(query, params)
                
                events = []
                for row in cursor.fetchall():
                    events.append(RiskEvent(
                        event_type=row['event_type'],
                        level=RiskLevel(row['level']),
                        symbol=row['symbol'],
                        strategy_name=row['strategy_name'],
                        description=row['description'],
                        details=json.loads(row['details']) if row['details'] else {},
                        timestamp=datetime.fromisoformat(row['timestamp'])
                    ))
                
                return events
        except Exception as e:
            risk_logger.error(f"获取风险事件失败: {e}")
            return []
    
    def update_risk_limits(self, new_limits: Dict[str, Any]):
        """更新风险限制"""
        for key, value in new_limits.items():
            if hasattr(self.risk_limits, key):
                setattr(self.risk_limits, key, value)
        
        # 重新初始化检查器
        self._init_default_checkers()
        risk_logger.info(f"风险限制已更新: {new_limits}")
    
    def get_risk_summary(self) -> Dict[str, Any]:
        """获取风险摘要"""
        today = datetime.now().date()
        recent_events = self.get_risk_events(
            start_date=today.isoformat(),
            end_date=(today + timedelta(days=1)).isoformat()
        )
        
        event_counts = {}
        for level in RiskLevel:
            event_counts[level.value] = len([e for e in recent_events if e.level == level])
        
        return {
            'enabled': self.enabled,
            'active_checkers': len([c for c in self.checkers if c.enabled]),
            'total_checkers': len(self.checkers),
            'today_events': len(recent_events),
            'event_counts_by_level': event_counts,
            'risk_limits': self.risk_limits.to_dict()
        }
    
    def enable(self):
        """启用风控"""
        self.enabled = True
        risk_logger.info("风控管理器已启用")
    
    def disable(self):
        """禁用风控"""
        self.enabled = False
        risk_logger.warning("风控管理器已禁用")


# 全局风控管理器实例
risk_manager = RiskManager()
