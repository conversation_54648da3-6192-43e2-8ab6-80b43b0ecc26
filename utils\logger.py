"""
日志管理模块
"""
import logging
import logging.handlers
import os
from datetime import datetime
from typing import Optional
from core.config import config_manager


class TradingLogger:
    """交易系统日志管理器"""
    
    def __init__(self, name: str = "TradingSystem"):
        self.name = name
        self.logger = logging.getLogger(name)
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志器"""
        # 清除现有处理器
        self.logger.handlers.clear()
        
        # 设置日志级别
        level = getattr(logging, config_manager.log.log_level.upper(), logging.INFO)
        self.logger.setLevel(level)
        
        # 创建日志目录
        log_dir = os.path.dirname(config_manager.log.log_file)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)
        
        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 文件处理器（带轮转）
        file_handler = logging.handlers.RotatingFileHandler(
            config_manager.log.log_file,
            maxBytes=config_manager.log.max_file_size,
            backupCount=config_manager.log.backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
    
    def debug(self, message: str, **kwargs):
        """调试日志"""
        self.logger.debug(message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """信息日志"""
        self.logger.info(message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """警告日志"""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """错误日志"""
        self.logger.error(message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """严重错误日志"""
        self.logger.critical(message, **kwargs)
    
    def log_trade(self, action: str, symbol: str, quantity: int, price: float, 
                  order_id: Optional[str] = None, **kwargs):
        """记录交易日志"""
        message = f"交易操作: {action} | 股票: {symbol} | 数量: {quantity} | 价格: {price:.2f}"
        if order_id:
            message += f" | 订单ID: {order_id}"
        
        for key, value in kwargs.items():
            message += f" | {key}: {value}"
        
        self.info(message)
    
    def log_strategy(self, strategy_name: str, action: str, details: str, **kwargs):
        """记录策略日志"""
        message = f"策略: {strategy_name} | 动作: {action} | 详情: {details}"
        
        for key, value in kwargs.items():
            message += f" | {key}: {value}"
        
        self.info(message)
    
    def log_risk(self, risk_type: str, level: str, message: str, **kwargs):
        """记录风控日志"""
        log_message = f"风控 [{level.upper()}]: {risk_type} | {message}"
        
        for key, value in kwargs.items():
            log_message += f" | {key}: {value}"
        
        if level.upper() == "CRITICAL":
            self.critical(log_message)
        elif level.upper() == "ERROR":
            self.error(log_message)
        elif level.upper() == "WARNING":
            self.warning(log_message)
        else:
            self.info(log_message)
    
    def log_market_data(self, symbol: str, price: float, volume: int = 0, **kwargs):
        """记录行情数据日志"""
        message = f"行情数据: {symbol} | 价格: {price:.2f}"
        if volume > 0:
            message += f" | 成交量: {volume}"
        
        for key, value in kwargs.items():
            message += f" | {key}: {value}"
        
        self.debug(message)
    
    def log_performance(self, strategy_name: str, metrics: dict):
        """记录策略性能日志"""
        message = f"策略性能: {strategy_name}"
        for metric, value in metrics.items():
            if isinstance(value, float):
                message += f" | {metric}: {value:.4f}"
            else:
                message += f" | {metric}: {value}"
        
        self.info(message)


class StrategyLogger(TradingLogger):
    """策略专用日志器"""
    
    def __init__(self, strategy_name: str):
        super().__init__(f"Strategy.{strategy_name}")
        self.strategy_name = strategy_name
    
    def log_signal(self, symbol: str, signal_type: str, signal_strength: float, 
                   reason: str, **kwargs):
        """记录交易信号"""
        message = f"交易信号: {symbol} | 类型: {signal_type} | 强度: {signal_strength:.2f} | 原因: {reason}"
        
        for key, value in kwargs.items():
            message += f" | {key}: {value}"
        
        self.info(message)
    
    def log_position_change(self, symbol: str, old_position: int, new_position: int, 
                           reason: str, **kwargs):
        """记录仓位变化"""
        change = new_position - old_position
        message = f"仓位变化: {symbol} | 原仓位: {old_position} | 新仓位: {new_position} | 变化: {change:+d} | 原因: {reason}"
        
        for key, value in kwargs.items():
            message += f" | {key}: {value}"
        
        self.info(message)


class RiskLogger(TradingLogger):
    """风控专用日志器"""
    
    def __init__(self):
        super().__init__("RiskManager")
    
    def log_risk_check(self, check_type: str, result: bool, details: str, **kwargs):
        """记录风控检查"""
        status = "通过" if result else "拒绝"
        message = f"风控检查: {check_type} | 结果: {status} | 详情: {details}"
        
        for key, value in kwargs.items():
            message += f" | {key}: {value}"
        
        if result:
            self.info(message)
        else:
            self.warning(message)
    
    def log_limit_breach(self, limit_type: str, current_value: float, 
                        limit_value: float, action: str, **kwargs):
        """记录限制突破"""
        message = f"限制突破: {limit_type} | 当前值: {current_value:.4f} | 限制值: {limit_value:.4f} | 处理动作: {action}"
        
        for key, value in kwargs.items():
            message += f" | {key}: {value}"
        
        self.error(message)


# 全局日志器实例
main_logger = TradingLogger("Main")
trade_logger = TradingLogger("Trade")
risk_logger = RiskLogger()


def get_strategy_logger(strategy_name: str) -> StrategyLogger:
    """获取策略日志器"""
    return StrategyLogger(strategy_name)


def get_logger(name: str) -> TradingLogger:
    """获取指定名称的日志器"""
    return TradingLogger(name)
