"""
卖出策略实现
基于原有卖出策略重构，集成到新的策略框架中
"""
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from core.base_strategy import BaseStrategy, Signal, StrategyParams, Position
from core.market_data_manager import MarketData


@dataclass
class SellStrategyParams(StrategyParams):
    """卖出策略参数"""
    # 止损百分比配置
    stop_loss_percent: Dict[str, float] = None
    
    # 绝对止损价格配置
    absolute_stop_loss_price: Dict[str, float] = None
    
    # 止盈百分比配置（可选）
    take_profit_percent: Dict[str, float] = None
    
    # 卖出数量类型：ALL, HALF, PERCENT_OF_POSITION
    sell_quantity_type: str = "ALL"
    
    # 按比例卖出时的比例
    sell_percent_of_position: float = 0.5
    
    # 订单类型：LIMIT_PRICE, MARKET_PRICE
    order_type: str = "LIMIT_PRICE"
    
    # 限价单价格策略：CURRENT_PRICE, BID_PRICE1, ASK_PRICE1
    limit_price_strategy: str = "BID_PRICE1"
    
    def __post_init__(self):
        super().__post_init__()
        
        # 默认止损配置
        if self.stop_loss_percent is None:
            self.stop_loss_percent = {
                "688648.SH": 0.02,  # 泽璟制药
                "600353.SH": 0.05,  # ST东源
                "600105.SH": 0.02,  # 永鼎股份
                "603900.SH": 0.02,  # 莱绅通灵
                "600202.SH": 0.02,  # 哈空调
                "000881.SZ": 0.03,  # 中广天择
                "603068.SH": 0.02,  # 博通集成
                "600756.SH": 0.02,  # 浪潮软件
                "601727.SH": 0.02,  # 上海电气
                "600468.SH": 0.02,  # 百利电气
            }
        
        # 默认绝对止损价格
        if self.absolute_stop_loss_price is None:
            self.absolute_stop_loss_price = {
                "688648.SH": 54.00,
                "600353.SH": 10.00,
                "600105.SH": 8.00,
                "603900.SH": 12.00,
                "600202.SH": 5.50,
                "000881.SZ": 7.80,
                "603068.SH": 32.50,
                "600756.SH": 15.00,
                "601727.SH": 7.70,
                "600468.SH": 5.80,
            }
        
        # 默认止盈配置（可选）
        if self.take_profit_percent is None:
            self.take_profit_percent = {}


class SellStrategy(BaseStrategy):
    """卖出策略"""
    
    def __init__(self, name: str, params: SellStrategyParams):
        super().__init__(name, params)
        self.params: SellStrategyParams = params
        
        # 监控的股票列表
        self.monitored_symbols = set()
        self.monitored_symbols.update(params.stop_loss_percent.keys())
        self.monitored_symbols.update(params.absolute_stop_loss_price.keys())
        
        # 最后检查时间
        self.last_check_time = {}
    
    def initialize(self) -> bool:
        """初始化策略"""
        try:
            self.logger.info(f"初始化卖出策略，监控股票: {list(self.monitored_symbols)}")
            
            # 订阅行情数据
            from core.market_data_manager import market_data_manager
            for symbol in self.monitored_symbols:
                market_data_manager.subscribe_symbol(symbol, self._on_market_data_callback)
            
            return True
            
        except Exception as e:
            self.logger.error(f"策略初始化失败: {e}")
            return False
    
    def generate_signals(self, market_data: Dict[str, Any]) -> List[Signal]:
        """生成交易信号"""
        signals = []
        
        # 遍历所有持仓，检查是否需要卖出
        for symbol, position in self.positions.items():
            if symbol not in self.monitored_symbols:
                continue
            
            if position.quantity <= 0:
                continue
            
            # 获取当前行情
            current_price = market_data.get('price', position.current_price)
            bid_price = market_data.get('bid_price', current_price)
            ask_price = market_data.get('ask_price', current_price)
            
            # 检查卖出条件
            sell_signal = self._check_sell_conditions(symbol, position, current_price, bid_price, ask_price)
            
            if sell_signal:
                signals.append(sell_signal)
        
        return signals
    
    def on_market_data(self, symbol: str, data: Dict[str, Any]):
        """处理市场数据"""
        if symbol not in self.monitored_symbols:
            return
        
        # 更新最后检查时间
        self.last_check_time[symbol] = datetime.now()
        
        # 检查是否有持仓
        position = self.get_position(symbol)
        if not position or position.quantity <= 0:
            return
        
        # 生成信号
        signals = self.generate_signals(data)
        
        # 添加信号
        for signal in signals:
            self.add_signal(signal)
    
    def on_trade_filled(self, symbol: str, action: str, quantity: int, price: float, order_id: str):
        """处理成交回报"""
        if action == 'sell':
            self.logger.info(f"卖出成交: {symbol} 数量:{quantity} 价格:{price:.2f} 订单:{order_id}")
            
            # 更新持仓
            position = self.get_position(symbol)
            if position:
                new_quantity = max(0, position.quantity - quantity)
                if new_quantity > 0:
                    self.update_position(symbol, new_quantity, position.avg_cost, price)
                else:
                    # 清空持仓
                    if symbol in self.positions:
                        del self.positions[symbol]
                    self.logger.info(f"股票 {symbol} 已清仓")
    
    def _on_market_data_callback(self, market_data: MarketData):
        """行情数据回调"""
        self.on_market_data(market_data.symbol, market_data.to_dict())
    
    def _check_sell_conditions(self, symbol: str, position: Position, 
                              current_price: float, bid_price: float, ask_price: float) -> Optional[Signal]:
        """检查卖出条件"""
        
        # 计算止损价格
        stop_loss_price_pct = None
        if symbol in self.params.stop_loss_percent:
            stop_loss_price_pct = position.avg_cost * (1 - self.params.stop_loss_percent[symbol])
        
        # 获取绝对止损价格
        absolute_stop_loss = self.params.absolute_stop_loss_price.get(symbol)
        
        # 确定最终止损价格（优先使用绝对止损价）
        final_stop_loss_price = absolute_stop_loss if absolute_stop_loss else stop_loss_price_pct
        
        # 计算止盈价格
        take_profit_price = None
        if symbol in self.params.take_profit_percent:
            take_profit_price = position.avg_cost * (1 + self.params.take_profit_percent[symbol])
        
        # 检查止损条件
        if final_stop_loss_price and current_price <= final_stop_loss_price:
            sell_price = self._calculate_sell_price(current_price, bid_price, ask_price, is_stop_loss=True)
            sell_quantity = self._calculate_sell_quantity(position.quantity)
            
            return Signal(
                symbol=symbol,
                action='sell',
                quantity=sell_quantity,
                price=sell_price,
                confidence=0.9,  # 止损信号置信度高
                reason=f"止损触发: 当前价 {current_price:.2f} <= 止损价 {final_stop_loss_price:.2f}",
                timestamp=datetime.now(),
                metadata={
                    'trigger_type': 'stop_loss',
                    'stop_loss_price': final_stop_loss_price,
                    'current_price': current_price,
                    'position_cost': position.avg_cost
                }
            )
        
        # 检查止盈条件
        if take_profit_price and current_price >= take_profit_price:
            sell_price = self._calculate_sell_price(current_price, bid_price, ask_price, is_stop_loss=False)
            sell_quantity = self._calculate_sell_quantity(position.quantity)
            
            return Signal(
                symbol=symbol,
                action='sell',
                quantity=sell_quantity,
                price=sell_price,
                confidence=0.7,  # 止盈信号置信度中等
                reason=f"止盈触发: 当前价 {current_price:.2f} >= 止盈价 {take_profit_price:.2f}",
                timestamp=datetime.now(),
                metadata={
                    'trigger_type': 'take_profit',
                    'take_profit_price': take_profit_price,
                    'current_price': current_price,
                    'position_cost': position.avg_cost
                }
            )
        
        # 可以在这里添加更多卖出条件
        # 例如：技术指标、形态分析、时间止损等
        
        return None
    
    def _calculate_sell_price(self, current_price: float, bid_price: float, 
                             ask_price: float, is_stop_loss: bool = False) -> float:
        """计算卖出价格"""
        if self.params.order_type == "MARKET_PRICE":
            return 0.0  # 市价单
        
        # 限价单价格策略
        if self.params.limit_price_strategy == "BID_PRICE1" and bid_price > 0:
            sell_price = bid_price
        elif self.params.limit_price_strategy == "ASK_PRICE1" and ask_price > 0:
            sell_price = ask_price
        else:  # CURRENT_PRICE
            sell_price = current_price
        
        # 止损时确保能够成交
        if is_stop_loss:
            # 止损时可以适当降低价格确保成交
            sell_price = min(sell_price, current_price * 0.99)
        
        return round(sell_price, 2)
    
    def _calculate_sell_quantity(self, available_quantity: int) -> int:
        """计算卖出数量"""
        if self.params.sell_quantity_type == "ALL":
            sell_quantity = available_quantity
        elif self.params.sell_quantity_type == "HALF":
            sell_quantity = available_quantity // 2
        elif self.params.sell_quantity_type == "PERCENT_OF_POSITION":
            sell_quantity = int(available_quantity * self.params.sell_percent_of_position)
        else:
            sell_quantity = available_quantity
        
        # 确保卖出数量是100的倍数（A股规则），不足100股全部卖出
        if sell_quantity < 100 and sell_quantity > 0:
            sell_quantity = available_quantity
        elif sell_quantity >= 100:
            sell_quantity = (sell_quantity // 100) * 100
        
        return max(0, sell_quantity)
    
    def add_monitored_symbol(self, symbol: str, stop_loss_percent: float = None, 
                           absolute_stop_loss: float = None, take_profit_percent: float = None):
        """添加监控股票"""
        self.monitored_symbols.add(symbol)
        
        if stop_loss_percent is not None:
            self.params.stop_loss_percent[symbol] = stop_loss_percent
        
        if absolute_stop_loss is not None:
            self.params.absolute_stop_loss_price[symbol] = absolute_stop_loss
        
        if take_profit_percent is not None:
            self.params.take_profit_percent[symbol] = take_profit_percent
        
        # 订阅行情
        from core.market_data_manager import market_data_manager
        market_data_manager.subscribe_symbol(symbol, self._on_market_data_callback)
        
        self.logger.info(f"添加监控股票: {symbol}")
    
    def remove_monitored_symbol(self, symbol: str):
        """移除监控股票"""
        if symbol in self.monitored_symbols:
            self.monitored_symbols.remove(symbol)
            
            # 取消订阅行情
            from core.market_data_manager import market_data_manager
            market_data_manager.unsubscribe_symbol(symbol, self._on_market_data_callback)
            
            self.logger.info(f"移除监控股票: {symbol}")
    
    def update_stop_loss(self, symbol: str, stop_loss_percent: float = None, 
                        absolute_stop_loss: float = None):
        """更新止损设置"""
        if stop_loss_percent is not None:
            self.params.stop_loss_percent[symbol] = stop_loss_percent
        
        if absolute_stop_loss is not None:
            self.params.absolute_stop_loss_price[symbol] = absolute_stop_loss
        
        self.logger.info(f"更新 {symbol} 止损设置")
    
    def get_monitored_symbols(self) -> List[str]:
        """获取监控股票列表"""
        return list(self.monitored_symbols)
