"""
示例策略实现
"""
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import pandas as pd
import numpy as np
from core.base_strategy import BaseStrategy, Signal, StrategyParams, Position


@dataclass
class MACDStrategyParams(StrategyParams):
    """MACD策略参数"""
    fast_period: int = 12
    slow_period: int = 26
    signal_period: int = 9
    symbols: List[str] = None
    
    def __post_init__(self):
        super().__post_init__()
        if self.symbols is None:
            self.symbols = ["000001.SZ", "000002.SZ", "600000.SH"]


class MACDStrategy(BaseStrategy):
    """MACD策略示例"""
    
    def __init__(self, name: str, params: MACDStrategyParams):
        super().__init__(name, params)
        self.params: MACDStrategyParams = params
        self.price_history: Dict[str, List[float]] = {}
        self.macd_data: Dict[str, Dict[str, float]] = {}
        
        # 初始化价格历史
        for symbol in self.params.symbols:
            self.price_history[symbol] = []
            self.macd_data[symbol] = {
                'macd': 0.0,
                'signal': 0.0,
                'histogram': 0.0,
                'prev_histogram': 0.0
            }
    
    def initialize(self) -> bool:
        """初始化策略"""
        try:
            self.logger.info(f"初始化MACD策略，监控股票: {self.params.symbols}")
            
            # 订阅行情数据
            from core.market_data_manager import market_data_manager
            for symbol in self.params.symbols:
                market_data_manager.subscribe_symbol(symbol, self._on_market_data_callback)
            
            return True
            
        except Exception as e:
            self.logger.error(f"MACD策略初始化失败: {e}")
            return False
    
    def generate_signals(self, market_data: Dict[str, Any]) -> List[Signal]:
        """生成交易信号"""
        signals = []
        symbol = market_data.get('symbol')
        
        if symbol not in self.params.symbols:
            return signals
        
        price = market_data.get('price', 0)
        if price <= 0:
            return signals
        
        # 更新价格历史
        self.price_history[symbol].append(price)
        
        # 保持历史数据长度
        max_length = max(self.params.slow_period * 3, 100)
        if len(self.price_history[symbol]) > max_length:
            self.price_history[symbol] = self.price_history[symbol][-max_length:]
        
        # 计算MACD
        if len(self.price_history[symbol]) >= self.params.slow_period:
            macd_result = self._calculate_macd(symbol)
            
            if macd_result:
                # 检查交易信号
                signal = self._check_macd_signals(symbol, price, macd_result)
                if signal:
                    signals.append(signal)
        
        return signals
    
    def on_market_data(self, symbol: str, data: Dict[str, Any]):
        """处理市场数据"""
        data['symbol'] = symbol
        signals = self.generate_signals(data)
        
        for signal in signals:
            self.add_signal(signal)
    
    def on_trade_filled(self, symbol: str, action: str, quantity: int, price: float, order_id: str):
        """处理成交回报"""
        self.logger.info(f"MACD策略成交: {action} {symbol} {quantity}@{price:.2f}")
        
        if action == 'buy':
            # 更新持仓
            current_position = self.get_position(symbol)
            if current_position:
                total_quantity = current_position.quantity + quantity
                total_cost = current_position.avg_cost * current_position.quantity + price * quantity
                new_avg_cost = total_cost / total_quantity
                self.update_position(symbol, total_quantity, new_avg_cost, price)
            else:
                self.update_position(symbol, quantity, price, price)
        
        elif action == 'sell':
            # 更新持仓
            current_position = self.get_position(symbol)
            if current_position:
                new_quantity = max(0, current_position.quantity - quantity)
                if new_quantity > 0:
                    self.update_position(symbol, new_quantity, current_position.avg_cost, price)
                else:
                    if symbol in self.positions:
                        del self.positions[symbol]
    
    def _on_market_data_callback(self, market_data):
        """行情数据回调"""
        self.on_market_data(market_data.symbol, market_data.to_dict())
    
    def _calculate_macd(self, symbol: str) -> Optional[Dict[str, float]]:
        """计算MACD指标"""
        try:
            prices = np.array(self.price_history[symbol])
            
            if len(prices) < self.params.slow_period:
                return None
            
            # 计算EMA
            ema_fast = self._calculate_ema(prices, self.params.fast_period)
            ema_slow = self._calculate_ema(prices, self.params.slow_period)
            
            # 计算MACD线
            macd_line = ema_fast - ema_slow
            
            # 计算信号线
            if len(self.price_history[symbol]) >= self.params.slow_period + self.params.signal_period:
                # 需要足够的MACD历史数据来计算信号线
                macd_history = []
                for i in range(len(prices) - self.params.signal_period + 1, len(prices) + 1):
                    if i >= self.params.slow_period:
                        temp_prices = prices[:i]
                        temp_ema_fast = self._calculate_ema(temp_prices, self.params.fast_period)
                        temp_ema_slow = self._calculate_ema(temp_prices, self.params.slow_period)
                        macd_history.append(temp_ema_fast - temp_ema_slow)
                
                if len(macd_history) >= self.params.signal_period:
                    signal_line = self._calculate_ema(np.array(macd_history), self.params.signal_period)
                else:
                    signal_line = macd_line
            else:
                signal_line = macd_line
            
            # 计算柱状图
            histogram = macd_line - signal_line
            
            # 保存前一个柱状图值
            prev_histogram = self.macd_data[symbol]['histogram']
            
            # 更新MACD数据
            self.macd_data[symbol] = {
                'macd': macd_line,
                'signal': signal_line,
                'histogram': histogram,
                'prev_histogram': prev_histogram
            }
            
            return self.macd_data[symbol]
            
        except Exception as e:
            self.logger.error(f"计算MACD失败: {e}")
            return None
    
    def _calculate_ema(self, prices: np.ndarray, period: int) -> float:
        """计算指数移动平均"""
        if len(prices) < period:
            return prices[-1]
        
        multiplier = 2.0 / (period + 1)
        ema = prices[0]
        
        for price in prices[1:]:
            ema = (price * multiplier) + (ema * (1 - multiplier))
        
        return ema
    
    def _check_macd_signals(self, symbol: str, current_price: float, 
                           macd_data: Dict[str, float]) -> Optional[Signal]:
        """检查MACD交易信号"""
        
        histogram = macd_data['histogram']
        prev_histogram = macd_data['prev_histogram']
        macd_line = macd_data['macd']
        signal_line = macd_data['signal']
        
        # 金叉信号（MACD线上穿信号线）
        if prev_histogram <= 0 and histogram > 0:
            # 检查是否已有持仓
            position = self.get_position(symbol)
            if not position or position.quantity == 0:
                return Signal(
                    symbol=symbol,
                    action='buy',
                    quantity=100,  # 默认买入100股
                    price=current_price,
                    confidence=0.7,
                    reason=f"MACD金叉: MACD={macd_line:.4f}, Signal={signal_line:.4f}",
                    timestamp=datetime.now(),
                    metadata={
                        'indicator': 'MACD',
                        'signal_type': 'golden_cross',
                        'macd': macd_line,
                        'signal': signal_line,
                        'histogram': histogram
                    }
                )
        
        # 死叉信号（MACD线下穿信号线）
        elif prev_histogram >= 0 and histogram < 0:
            # 检查是否有持仓
            position = self.get_position(symbol)
            if position and position.quantity > 0:
                return Signal(
                    symbol=symbol,
                    action='sell',
                    quantity=position.quantity,  # 全部卖出
                    price=current_price,
                    confidence=0.7,
                    reason=f"MACD死叉: MACD={macd_line:.4f}, Signal={signal_line:.4f}",
                    timestamp=datetime.now(),
                    metadata={
                        'indicator': 'MACD',
                        'signal_type': 'death_cross',
                        'macd': macd_line,
                        'signal': signal_line,
                        'histogram': histogram
                    }
                )
        
        return None


@dataclass
class MovingAverageStrategyParams(StrategyParams):
    """移动平均策略参数"""
    short_period: int = 5
    long_period: int = 20
    symbols: List[str] = None
    
    def __post_init__(self):
        super().__post_init__()
        if self.symbols is None:
            self.symbols = ["000001.SZ", "000002.SZ"]


class MovingAverageStrategy(BaseStrategy):
    """移动平均策略示例"""
    
    def __init__(self, name: str, params: MovingAverageStrategyParams):
        super().__init__(name, params)
        self.params: MovingAverageStrategyParams = params
        self.price_history: Dict[str, List[float]] = {}
        
        # 初始化价格历史
        for symbol in self.params.symbols:
            self.price_history[symbol] = []
    
    def initialize(self) -> bool:
        """初始化策略"""
        try:
            self.logger.info(f"初始化移动平均策略，监控股票: {self.params.symbols}")
            
            # 订阅行情数据
            from core.market_data_manager import market_data_manager
            for symbol in self.params.symbols:
                market_data_manager.subscribe_symbol(symbol, self._on_market_data_callback)
            
            return True
            
        except Exception as e:
            self.logger.error(f"移动平均策略初始化失败: {e}")
            return False
    
    def generate_signals(self, market_data: Dict[str, Any]) -> List[Signal]:
        """生成交易信号"""
        signals = []
        symbol = market_data.get('symbol')
        
        if symbol not in self.params.symbols:
            return signals
        
        price = market_data.get('price', 0)
        if price <= 0:
            return signals
        
        # 更新价格历史
        self.price_history[symbol].append(price)
        
        # 保持历史数据长度
        max_length = self.params.long_period * 2
        if len(self.price_history[symbol]) > max_length:
            self.price_history[symbol] = self.price_history[symbol][-max_length:]
        
        # 检查是否有足够的数据
        if len(self.price_history[symbol]) >= self.params.long_period:
            signal = self._check_ma_signals(symbol, price)
            if signal:
                signals.append(signal)
        
        return signals
    
    def on_market_data(self, symbol: str, data: Dict[str, Any]):
        """处理市场数据"""
        data['symbol'] = symbol
        signals = self.generate_signals(data)
        
        for signal in signals:
            self.add_signal(signal)
    
    def on_trade_filled(self, symbol: str, action: str, quantity: int, price: float, order_id: str):
        """处理成交回报"""
        self.logger.info(f"移动平均策略成交: {action} {symbol} {quantity}@{price:.2f}")
    
    def _on_market_data_callback(self, market_data):
        """行情数据回调"""
        self.on_market_data(market_data.symbol, market_data.to_dict())
    
    def _check_ma_signals(self, symbol: str, current_price: float) -> Optional[Signal]:
        """检查移动平均交易信号"""
        prices = self.price_history[symbol]
        
        if len(prices) < self.params.long_period:
            return None
        
        # 计算移动平均
        short_ma = np.mean(prices[-self.params.short_period:])
        long_ma = np.mean(prices[-self.params.long_period:])
        
        # 计算前一个周期的移动平均
        if len(prices) > self.params.long_period:
            prev_short_ma = np.mean(prices[-self.params.short_period-1:-1])
            prev_long_ma = np.mean(prices[-self.params.long_period-1:-1])
        else:
            return None
        
        # 金叉信号
        if prev_short_ma <= prev_long_ma and short_ma > long_ma:
            position = self.get_position(symbol)
            if not position or position.quantity == 0:
                return Signal(
                    symbol=symbol,
                    action='buy',
                    quantity=100,
                    price=current_price,
                    confidence=0.6,
                    reason=f"均线金叉: MA{self.params.short_period}={short_ma:.2f} > MA{self.params.long_period}={long_ma:.2f}",
                    timestamp=datetime.now(),
                    metadata={
                        'indicator': 'MA',
                        'signal_type': 'golden_cross',
                        'short_ma': short_ma,
                        'long_ma': long_ma
                    }
                )
        
        # 死叉信号
        elif prev_short_ma >= prev_long_ma and short_ma < long_ma:
            position = self.get_position(symbol)
            if position and position.quantity > 0:
                return Signal(
                    symbol=symbol,
                    action='sell',
                    quantity=position.quantity,
                    price=current_price,
                    confidence=0.6,
                    reason=f"均线死叉: MA{self.params.short_period}={short_ma:.2f} < MA{self.params.long_period}={long_ma:.2f}",
                    timestamp=datetime.now(),
                    metadata={
                        'indicator': 'MA',
                        'signal_type': 'death_cross',
                        'short_ma': short_ma,
                        'long_ma': long_ma
                    }
                )
        
        return None
