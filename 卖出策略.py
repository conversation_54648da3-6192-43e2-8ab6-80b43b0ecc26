from qmt.qmt_api import QMTClient
import time
import pandas as pd

# ==================== 1. QMT客户端初始化与账户配置 ====================
# 请根据您的实际QMT客户端安装路径和账户信息进行配置
QMT_PATH = "D:\\恒泰证券迅投QMT"  # 替换为您的QMT客户端安装路径
ACCOUNT_ID = "YOUR_ACCOUNT_ID"  # 替换为您的资金账号，例如 "********"
ACCOUNT_TYPE = "STOCK"  # 账户类型，股票账户为 "STOCK"

# 初始化QMT客户端
try:
    qmt_client = QMTClient(path=QMT_PATH)
    print(f"QMT客户端初始化成功，路径：{QMT_PATH}")
    # 尝试连接交易模块
    qmt_client.connect_trade(ACCOUNT_ID, ACCOUNT_TYPE)
    print(f"成功连接交易模块，账户ID：{ACCOUNT_ID}, 类型：{ACCOUNT_TYPE}")
except Exception as e:
    print(f"QMT客户端初始化或连接失败: {e}")
    exit()  # 如果连接失败，则退出程序

# ==================== 2. 策略参数配置 (根据您的具体卖出策略) ====================

# 止损百分比：当价格跌破成本价的某个百分比时止损
STOP_LOSS_PERCENT = {
    "688648.SH": 0.02,  # 泽璟制药，跌破成本价2%止损
    "600353.SH": 0.05,  # ST东源，ST股止损更严格，或直接设定绝对值
    "600105.SH": 0.02,  # 永鼎股份
    "603900.SH": 0.02,  # 莱绅通灵
    "600202.SH": 0.02,  # 哈空调
    "000881.SZ": 0.03,  # 中广天择
    "603068.SH": 0.02,  # 博通集成
    "600756.SH": 0.02,  # 浪潮软件
    "601727.SH": 0.02,  # 上海电气
    "600468.SH": 0.02,  # 百利电气
}

# 绝对止损价格：当价格跌破某个绝对值时止损
ABSOLUTE_STOP_LOSS_PRICE = {
    "688648.SH": 54.00,
    "600353.SH": 10.00,
    "600105.SH": 8.00,
    "603900.SH": 12.00,
    "600202.SH": 5.50,
    "000881.SZ": 7.80,
    "603068.SH": 32.50,
    "600756.SH": 15.00,
    "601727.SH": 7.70,
    "600468.SH": 5.80,
}

# 止盈百分比：当价格达到成本价的某个百分比时止盈（可选，如果需要止盈）
TAKE_PROFIT_PERCENT = {
    # "688648.SH": 0.05, # 例如，盈利5%时考虑部分止盈
}

# 卖出数量类型：可以是 "ALL" (全部卖出) 或 "HALF" (一半数量) 或 "PERCENT_OF_POSITION" (按持仓比例卖出)
# 如果是 "PERCENT_OF_POSITION"，请指定比例，例如 0.5 (50%)
SELL_QUANTITY_TYPE = "ALL"
SELL_PERCENT_OF_POSITION = 0.5  # 仅当 SELL_QUANTITY_TYPE 为 "PERCENT_OF_POSITION" 时生效

# 订单类型： "LIMIT_PRICE" (限价单) 或 "MARKET_PRICE" (市价单，注意可能无法买到理想价格)
ORDER_TYPE = "LIMIT_PRICE"

# 限价单价格策略：
# "CURRENT_PRICE" - 使用当前最新价（可能成交不了）
# "BID_PRICE1" - 使用买一价（更容易成交）
# "ASK_PRICE1" - 使用卖一价（如果想更快成交，买入用ASK1，卖出用BID1）
LIMIT_PRICE_STRATEGY = "BID_PRICE1"  # 卖出时用买一价更容易成交


# ==================== 3. 获取持仓函数 ====================
def get_current_positions(qmt_client, account_id, account_type):
    """
    获取当前账户的持仓信息。
    返回一个字典，key为股票代码，value为持仓详情（如成本价、可用数量等）。
    """
    try:
        positions_df = qmt_client.query_account_positions(account_id, account_type)
        if positions_df is None or positions_df.empty:
            print("未获取到持仓信息或持仓为空。")
            return {}

        positions = {}
        for index, row in positions_df.iterrows():
            stock_code = row['stock_code']
            # 将QMT返回的DataFrame列名映射到我们需要的字段
            positions[stock_code] = {
                'stock_name': row['stock_name'],
                'volume': int(row['current_amount']),  # 当前持仓数量
                'available_volume': int(row['enable_amount']),  # 可用数量
                'cost_price': float(row['cost_price']),  # 成本价
                'current_price': float(row['last_price'])  # 当前价格
            }
        print("当前持仓：")
        for code, info in positions.items():
            print(
                f"  {code} ({info['stock_name']}): 成本价={info['cost_price']:.2f}, 当前价={info['current_price']:.2f}, 可用量={info['available_volume']}")
        return positions
    except Exception as e:
        print(f"获取持仓失败: {e}")
        return {}


# ==================== 4. 获取实时行情函数 ====================
def get_realtime_quote(qmt_client, stock_code):
    """
    获取单只股票的实时行情数据。
    返回一个字典，包含'current_price', 'bid_price1', 'ask_price1'等。
    """
    try:
        quote_df = qmt_client.query_realtime_quotes([stock_code])
        if quote_df is None or quote_df.empty:
            print(f"未获取到 {stock_code} 的实时行情。")
            return None

        # QMT返回的行情数据可能包含多个字段，这里我们提取常用字段
        quote_data = {
            'current_price': float(quote_df.iloc[0]['last_price']),
            'bid_price1': float(quote_df.iloc[0]['bid_price1']),
            'bid_volume1': int(quote_df.iloc[0]['bid_volume1']),
            'ask_price1': float(quote_df.iloc[0]['ask_price1']),
            'ask_volume1': int(quote_df.iloc[0]['ask_volume1']),
            # 可以根据需要添加更多字段，如 'high_price', 'low_price', 'open_price' 等
        }
        return quote_data
    except Exception as e:
        print(f"获取 {stock_code} 实时行情失败: {e}")
        return None


# ==================== 5. 下单卖出函数 ====================
def place_sell_order(qmt_client, account_id, account_type, stock_code, sell_price, sell_volume, order_type):
    """
    下达卖出订单。
    """
    if sell_volume <= 0:
        print(f"股票 {stock_code} 卖出数量为0，不执行下单。")
        return None

    # 将价格四舍五入到小数点后两位
    sell_price = round(sell_price, 2)

    try:
        order_response = qmt_client.send_order(
            account_id=account_id,
            account_type=account_type,
            stock_code=stock_code,
            order_type=1,  # 订单类型: 1-限价委托, 2-市价委托 (QMT定义，非通用)
            price=sell_price,
            amount=int(sell_volume),  # 卖出数量必须是整数
            direction=2  # 交易方向: 1-买入, 2-卖出
        )
        # QMT的send_order返回的是一个字典，通常包含'order_id'和'error_code'/'error_msg'
        if order_response and order_response.get('error_code') == 0:
            order_id = order_response.get('order_id')
            print(f"成功提交 {stock_code} 卖出订单。订单ID: {order_id}, 价格: {sell_price:.2f}, 数量: {sell_volume}")
            return order_id
        else:
            error_msg = order_response.get('error_msg', '未知错误') if order_response else 'API调用无响应'
            print(f"提交 {stock_code} 卖出订单失败: {error_msg}")
            return None
    except Exception as e:
        print(f"下单失败 {stock_code}: {e}")
        return None


# ==================== 6. 主卖出策略逻辑 ====================
def run_sell_strategy():
    print("\n========= 卖出策略开始执行 =========")

    # 1. 获取当前持仓
    current_positions = get_current_positions(qmt_client, ACCOUNT_ID, ACCOUNT_TYPE)
    if not current_positions:
        print("无持仓或获取持仓失败，策略结束。")
        return

    # 2. 遍历持仓，根据策略判断是否卖出
    for stock_code, position_info in current_positions.items():
        stock_name = position_info['stock_name']
        cost_price = position_info['cost_price']
        available_volume = position_info['available_volume']

        print(f"\n--- 正在评估 {stock_code} ({stock_name}) ---")
        print(f"  成本价: {cost_price:.2f}, 可用数量: {available_volume}")

        if available_volume == 0:
            print(f"  {stock_code} 可用数量为0，跳过。")
            continue

        # 获取实时行情
        quote = get_realtime_quote(qmt_client, stock_code)
        if not quote:
            print(f"  无法获取 {stock_code} 实时行情，跳过。")
            continue

        current_price = quote['current_price']
        bid_price1 = quote['bid_price1']  # 买一价
        ask_price1 = quote['ask_price1']  # 卖一价

        print(f"  当前价: {current_price:.2f}, 买一价: {bid_price1:.2f}, 卖一价: {ask_price1:.2f}")

        # 计算止损和止盈价格
        calculated_stop_loss_price_pct = cost_price * (1 - STOP_LOSS_PERCENT.get(stock_code, 0))  # 默认不触发
        calculated_take_profit_price_pct = cost_price * (1 + TAKE_PROFIT_PERCENT.get(stock_code, 999))  # 默认不触发

        # 优先使用绝对止损价，如果没有设置，则使用百分比止损价
        final_stop_loss_price = ABSOLUTE_STOP_LOSS_PRICE.get(stock_code)
        if final_stop_loss_price is None:  # 如果没有设置绝对止损价，则使用百分比止损价
            if STOP_LOSS_PERCENT.get(stock_code) is not None:
                final_stop_loss_price = calculated_stop_loss_price_pct
            else:
                final_stop_loss_price = float('-inf')  # 如果没有设置止损，则设置为一个极小值，确保不触发

        # 判断是否触发卖出条件
        sell_triggered = False
        sell_reason = ""
        sell_price = current_price  # 默认卖出价格

        # 止损判断
        if current_price <= final_stop_loss_price and final_stop_loss_price != float('-inf'):
            sell_triggered = True
            sell_reason = f"止损触发: 当前价 {current_price:.2f} <= 止损价 {final_stop_loss_price:.2f}"
            if ORDER_TYPE == "LIMIT_PRICE":
                # 卖出时，限价单通常挂买一价更容易成交
                sell_price = bid_price1 if bid_price1 > 0 else current_price
                # 确保止损单能成交，如果买一价仍高于止损价，可以考虑设为止损价
                if sell_price > current_price:  # 如果买一价高于当前价，这在下跌时可能发生，确保能卖出
                    sell_price = current_price * 0.99  # 略低于当前价，确保能成交

                # 如果当前价已经低于止损价，那么为了确保成交，限价可能需要更低
                if current_price < final_stop_loss_price:
                    sell_price = current_price * 0.99

                    # 止盈判断 (如果需要)
        # elif current_price >= calculated_take_profit_price_pct and TAKE_PROFIT_PERCENT.get(stock_code) is not None:
        #     sell_triggered = True
        #     sell_reason = f"止盈触发: 当前价 {current_price:.2f} >= 止盈价 {calculated_take_profit_price_pct:.2f}"
        #     if ORDER_TYPE == "LIMIT_PRICE":
        #         sell_price = bid_price1 if bid_price1 > 0 else current_price

        # 可以添加更多复杂判断，例如：
        # - K线形态分析（如长上影线、M头等，需要K线数据和分析函数）
        # - 均线死叉（需要历史均线数据，并判断当前价与均线关系）
        # - MACD/KDJ指标背离或死叉（需要历史指标数据）
        # - 板块/概念整体走弱（需要获取板块指数或同概念股行情）
        # - 重大利空消息（需要外部消息源判断）

        # 示例：假设我们判断当股价跌破开盘价的99%时卖出 (仅为示例，实际需要开盘价数据)
        # if quote['open_price'] and current_price < quote['open_price'] * 0.99:
        #    sell_triggered = True
        #    sell_reason = "股价跌破开盘价99%"

        if sell_triggered:
            sell_volume_to_order = 0
            if SELL_QUANTITY_TYPE == "ALL":
                sell_volume_to_order = available_volume
            elif SELL_QUANTITY_TYPE == "HALF":
                sell_volume_to_order = available_volume // 2
            elif SELL_QUANTITY_TYPE == "PERCENT_OF_POSITION":
                sell_volume_to_order = int(available_volume * SELL_PERCENT_OF_POSITION)

            # 卖出数量必须是100股的整数倍（A股规则），不足100股全部卖出
            if sell_volume_to_order < 100 and sell_volume_to_order > 0:
                sell_volume_to_order = available_volume
            elif sell_volume_to_order >= 100:
                sell_volume_to_order = (sell_volume_to_order // 100) * 100  # 确保是100的倍数

            if sell_volume_to_order == 0:
                print(f"  {stock_code} 卖出数量计算为0，不执行卖出。")
                continue

            print(f"  **触发卖出条件** ({sell_reason})")
            print(f"  准备卖出 {stock_code}，数量: {sell_volume_to_order}股")

            # 根据订单类型设置卖出价格
            order_execute_price = sell_price
            if ORDER_TYPE == "LIMIT_PRICE":
                if LIMIT_PRICE_STRATEGY == "BID_PRICE1" and bid_price1 > 0:
                    order_execute_price = bid_price1
                elif LIMIT_PRICE_STRATEGY == "CURRENT_PRICE":
                    order_execute_price = current_price
                elif LIMIT_PRICE_STRATEGY == "ASK_PRICE1" and ask_price1 > 0:
                    # 卖出时挂卖一价意味着直接吃掉卖一，可能会导致价格不利，慎用
                    order_execute_price = ask_price1
                else:
                    order_execute_price = current_price  # 默认使用当前价

                # 限价单价格不能偏离当前价太多，尤其在跌停板等极端情况，可能需要调整
                # 简单处理：确保价格不低于当前价的99% (防止极端低价成交)
                order_execute_price = max(order_execute_price, current_price * 0.99)

            elif ORDER_TYPE == "MARKET_PRICE":
                order_execute_price = 0.0  # 市价单价格填0

            # 提交卖出订单
            placed_order_id = place_sell_order(
                qmt_client,
                ACCOUNT_ID,
                ACCOUNT_TYPE,
                stock_code,
                order_execute_price,
                sell_volume_to_order,
                ORDER_TYPE
            )
            if placed_order_id:
                print(f"  {stock_code} 卖出订单已提交，订单ID：{placed_order_id}")
            else:
                print(f"  {stock_code} 卖出订单提交失败。")
        else:
            print(f"  {stock_code} 未触发卖出条件。")

    print("\n========= 卖出策略执行完毕 =========")


# ==================== 7. 策略运行入口 ====================
if __name__ == "__main__":
    # 可以在交易时间内循环执行策略
    # 注意：过度频繁地查询和下单可能会触发API限制或导致程序不稳定
    # 建议设置合理的查询间隔和交易时间段

    # 示例：只在交易时间段内执行一次
    # 如果想在交易时间内持续监控，可以使用循环
    # 例如：
    # while True:
    #     current_time = time.localtime()
    #     # 设置交易时间段（例如：9:30 - 11:30, 13:00 - 15:00）
    #     if (9 <= current_time.tm_hour < 11 and current_time.tm_min >= 30) or \
    #        (current_time.tm_hour == 11 and current_time.tm_min <= 30) or \
    #        (13 <= current_time.tm_hour < 15):
    #         run_sell_strategy()
    #         time.sleep(60) # 每60秒检查一次
    #     else:
    #         print("非交易时间，等待...")
    #         time.sleep(300) # 非交易时间，等待5分钟再检查

    # 简单示例：直接执行一次
    run_sell_strategy()

    # 策略执行完毕后，断开QMT连接
    qmt_client.disconnect_trade(ACCOUNT_ID, ACCOUNT_TYPE)
    print("QMT交易模块已断开连接。")

